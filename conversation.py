"""
Conversation management for persistent chat sessions.

This module provides conversation persistence, history management,
and session handling for the AI agent.
"""

import json
import uuid
from datetime import datetime
from pathlib import Path
from typing import List, Optional, Dict, Any

from message import Message, messages_to_dict_list, messages_from_dict_list
from config import get_config

class ConversationManager:
    """
    Manages conversation persistence and history.
    
    Handles saving/loading conversations, managing message history,
    and providing conversation metadata.
    """
    
    def __init__(self, conversation_id: Optional[str] = None):
        """
        Initialize conversation manager.
        
        Args:
            conversation_id: Existing conversation ID or None for new conversation
        """
        self.config = get_config()
        self.conversation_id = conversation_id or str(uuid.uuid4())
        self.messages: List[Message] = []
        self.metadata: Dict[str, Any] = {
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat(),
            "title": None,
            "tags": [],
            "model": self.config.llm.model,
            "tools": self.config.tools.enabled.copy()
        }
        
        # Load existing conversation if ID provided
        if conversation_id:
            self.load()
    
    @property
    def conversation_file(self) -> Path:
        """Get the conversation file path."""
        return self.config.logs_dir / f"{self.conversation_id}.json"
    
    def add_message(self, message: Message) -> None:
        """
        Add a message to the conversation.
        
        Args:
            message: Message to add
        """
        self.messages.append(message)
        self.metadata["updated_at"] = datetime.now().isoformat()
        
        # Auto-generate title from first user message
        if (not self.metadata["title"] and 
            message.role == "user" and 
            len(self.messages) <= 3):
            self.metadata["title"] = self._generate_title(message.content)
    
    def remove_message(self, index: int) -> Optional[Message]:
        """
        Remove a message by index.
        
        Args:
            index: Index of message to remove
        
        Returns:
            Removed message or None if index invalid
        """
        if 0 <= index < len(self.messages):
            message = self.messages.pop(index)
            self.metadata["updated_at"] = datetime.now().isoformat()
            return message
        return None
    
    def clear(self) -> None:
        """Clear all messages from the conversation."""
        self.messages.clear()
        self.metadata["updated_at"] = datetime.now().isoformat()
    
    def get_messages(self, limit: Optional[int] = None) -> List[Message]:
        """
        Get conversation messages.
        
        Args:
            limit: Maximum number of messages to return
        
        Returns:
            List of messages
        """
        if limit is None:
            return self.messages.copy()
        return self.messages[-limit:] if limit > 0 else []
    
    def get_message_count(self) -> int:
        """Get the total number of messages."""
        return len(self.messages)
    
    def get_user_messages(self) -> List[Message]:
        """Get only user messages."""
        return [msg for msg in self.messages if msg.role == "user"]
    
    def get_assistant_messages(self) -> List[Message]:
        """Get only assistant messages."""
        return [msg for msg in self.messages if msg.role == "assistant"]
    
    def search_messages(self, query: str, case_sensitive: bool = False) -> List[tuple[int, Message]]:
        """
        Search for messages containing a query string.
        
        Args:
            query: Search query
            case_sensitive: Whether search is case sensitive
        
        Returns:
            List of (index, message) tuples
        """
        results = []
        search_query = query if case_sensitive else query.lower()
        
        for i, message in enumerate(self.messages):
            content = message.content if case_sensitive else message.content.lower()
            if search_query in content:
                results.append((i, message))
        
        return results
    
    def get_conversation_stats(self) -> Dict[str, Any]:
        """
        Get conversation statistics.
        
        Returns:
            Dictionary with conversation stats
        """
        user_msgs = self.get_user_messages()
        assistant_msgs = self.get_assistant_messages()
        
        total_chars = sum(len(msg.content) for msg in self.messages)
        total_words = sum(len(msg.content.split()) for msg in self.messages)
        
        return {
            "total_messages": len(self.messages),
            "user_messages": len(user_msgs),
            "assistant_messages": len(assistant_msgs),
            "total_characters": total_chars,
            "total_words": total_words,
            "created_at": self.metadata["created_at"],
            "updated_at": self.metadata["updated_at"],
            "title": self.metadata["title"],
            "model": self.metadata["model"],
            "tools": self.metadata["tools"]
        }
    
    def save(self) -> None:
        """Save conversation to file."""
        try:
            # Ensure logs directory exists
            self.config.logs_dir.mkdir(parents=True, exist_ok=True)
            
            # Prepare data for saving
            data = {
                "conversation_id": self.conversation_id,
                "metadata": self.metadata,
                "messages": messages_to_dict_list(self.messages)
            }
            
            # Save to file
            with open(self.conversation_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            print(f"Error saving conversation: {e}")
    
    def load(self) -> bool:
        """
        Load conversation from file.
        
        Returns:
            True if loaded successfully, False otherwise
        """
        try:
            if not self.conversation_file.exists():
                return False
            
            with open(self.conversation_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Load metadata
            self.metadata = data.get("metadata", self.metadata)
            
            # Load messages
            messages_data = data.get("messages", [])
            self.messages = messages_from_dict_list(messages_data)
            
            return True
            
        except Exception as e:
            print(f"Error loading conversation: {e}")
            return False
    
    def export_to_markdown(self, file_path: Optional[Path] = None) -> str:
        """
        Export conversation to markdown format.
        
        Args:
            file_path: Optional file path to save markdown
        
        Returns:
            Markdown content
        """
        lines = []
        
        # Add header
        title = self.metadata.get("title", f"Conversation {self.conversation_id}")
        lines.append(f"# {title}")
        lines.append("")
        lines.append(f"**Created:** {self.metadata['created_at']}")
        lines.append(f"**Updated:** {self.metadata['updated_at']}")
        lines.append(f"**Model:** {self.metadata['model']}")
        lines.append(f"**Messages:** {len(self.messages)}")
        lines.append("")
        lines.append("---")
        lines.append("")
        
        # Add messages
        for i, message in enumerate(self.messages):
            # Add role header
            role_emoji = {
                "user": "👤",
                "assistant": "🤖", 
                "system": "⚙️"
            }.get(message.role, "❓")
            
            lines.append(f"## {role_emoji} {message.role.title()}")
            lines.append("")
            
            # Add timestamp
            lines.append(f"*{message.timestamp.strftime('%Y-%m-%d %H:%M:%S')}*")
            lines.append("")
            
            # Add content
            lines.append(message.content)
            lines.append("")
            
            # Add separator (except for last message)
            if i < len(self.messages) - 1:
                lines.append("---")
                lines.append("")
        
        markdown_content = "\n".join(lines)
        
        # Save to file if path provided
        if file_path:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(markdown_content)
        
        return markdown_content
    
    def _generate_title(self, content: str, max_length: int = 50) -> str:
        """
        Generate a title from message content.
        
        Args:
            content: Message content
            max_length: Maximum title length
        
        Returns:
            Generated title
        """
        # Clean and truncate content
        title = content.strip().replace('\n', ' ')
        
        # Remove code blocks and special formatting
        import re
        title = re.sub(r'```.*?```', '[code]', title, flags=re.DOTALL)
        title = re.sub(r'`[^`]+`', '[code]', title)
        title = re.sub(r'\*\*([^*]+)\*\*', r'\1', title)
        title = re.sub(r'\*([^*]+)\*', r'\1', title)
        
        # Truncate if too long
        if len(title) > max_length:
            title = title[:max_length-3] + "..."
        
        return title
    
    def __str__(self) -> str:
        """String representation of the conversation."""
        return f"Conversation({self.conversation_id}, {len(self.messages)} messages)"
    
    def __repr__(self) -> str:
        """Detailed string representation."""
        return (f"ConversationManager(id='{self.conversation_id}', "
                f"messages={len(self.messages)}, "
                f"title='{self.metadata.get('title', 'Untitled')}')")


def list_conversations(config: Optional[Any] = None) -> List[Dict[str, Any]]:
    """
    List all saved conversations.
    
    Args:
        config: Configuration object (optional)
    
    Returns:
        List of conversation metadata
    """
    if config is None:
        config = get_config()
    
    conversations = []
    
    if not config.logs_dir.exists():
        return conversations
    
    for file_path in config.logs_dir.glob("*.json"):
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            metadata = data.get("metadata", {})
            conversations.append({
                "conversation_id": data.get("conversation_id"),
                "title": metadata.get("title", "Untitled"),
                "created_at": metadata.get("created_at"),
                "updated_at": metadata.get("updated_at"),
                "message_count": len(data.get("messages", [])),
                "model": metadata.get("model"),
                "file_path": str(file_path)
            })
            
        except Exception as e:
            print(f"Error reading conversation file {file_path}: {e}")
    
    # Sort by updated_at (most recent first)
    conversations.sort(key=lambda x: x.get("updated_at", ""), reverse=True)
    
    return conversations


__all__ = [
    "ConversationManager",
    "list_conversations",
]
