"""
Advanced process management tool for system interaction and automation.

This tool provides comprehensive process management capabilities including
launching, monitoring, controlling, and communicating with system processes.
"""

import os
import sys
import subprocess
import threading
import time
import signal
import logging
from typing import Generator, Dict, Any, List, Optional, Tuple
from pathlib import Path
from dataclasses import dataclass
from enum import Enum
import psutil

from tools.base import ToolSpec, Parameter
from message import Message

logger = logging.getLogger(__name__)


class ProcessState(Enum):
    """Process states."""
    RUNNING = "running"
    STOPPED = "stopped"
    FINISHED = "finished"
    ERROR = "error"
    KILLED = "killed"


@dataclass
class ProcessInfo:
    """Information about a managed process."""
    pid: int
    command: str
    state: ProcessState
    start_time: float
    end_time: Optional[float] = None
    return_code: Optional[int] = None
    stdout_buffer: List[str] = None
    stderr_buffer: List[str] = None
    working_dir: Optional[str] = None
    environment: Optional[Dict[str, str]] = None
    
    def __post_init__(self):
        if self.stdout_buffer is None:
            self.stdout_buffer = []
        if self.stderr_buffer is None:
            self.stderr_buffer = []


class ProcessManager:
    """Advanced process management system."""
    
    def __init__(self):
        """Initialize process manager."""
        self.processes: Dict[int, ProcessInfo] = {}
        self.subprocess_handles: Dict[int, subprocess.Popen] = {}
        self._lock = threading.Lock()
        self._output_threads: Dict[int, List[threading.Thread]] = {}
    
    def launch_process(
        self,
        command: str,
        working_dir: Optional[str] = None,
        environment: Optional[Dict[str, str]] = None,
        wait: bool = False,
        wait_timeout: int = 60,
        capture_output: bool = True,
        shell: bool = True
    ) -> ProcessInfo:
        """Launch a new process."""
        try:
            # Prepare environment
            env = os.environ.copy()
            if environment:
                env.update(environment)
            
            # Launch process
            process = subprocess.Popen(
                command,
                shell=shell,
                cwd=working_dir,
                env=env,
                stdout=subprocess.PIPE if capture_output else None,
                stderr=subprocess.PIPE if capture_output else None,
                stdin=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            # Create process info
            process_info = ProcessInfo(
                pid=process.pid,
                command=command,
                state=ProcessState.RUNNING,
                start_time=time.time(),
                working_dir=working_dir,
                environment=environment
            )
            
            with self._lock:
                self.processes[process.pid] = process_info
                self.subprocess_handles[process.pid] = process
            
            # Start output capture threads if needed
            if capture_output:
                self._start_output_capture(process.pid, process)
            
            # Wait for completion if requested
            if wait:
                try:
                    return_code = process.wait(timeout=wait_timeout)
                    process_info.return_code = return_code
                    process_info.end_time = time.time()
                    process_info.state = ProcessState.FINISHED
                except subprocess.TimeoutExpired:
                    # Process is still running after timeout
                    pass
            
            return process_info
            
        except Exception as e:
            logger.error(f"Failed to launch process: {e}")
            raise
    
    def _start_output_capture(self, pid: int, process: subprocess.Popen) -> None:
        """Start threads to capture process output."""
        def capture_stdout():
            try:
                for line in iter(process.stdout.readline, ''):
                    if line:
                        with self._lock:
                            if pid in self.processes:
                                self.processes[pid].stdout_buffer.append(line.rstrip())
            except Exception as e:
                logger.debug(f"Stdout capture error for PID {pid}: {e}")
        
        def capture_stderr():
            try:
                for line in iter(process.stderr.readline, ''):
                    if line:
                        with self._lock:
                            if pid in self.processes:
                                self.processes[pid].stderr_buffer.append(line.rstrip())
            except Exception as e:
                logger.debug(f"Stderr capture error for PID {pid}: {e}")
        
        stdout_thread = threading.Thread(target=capture_stdout, daemon=True)
        stderr_thread = threading.Thread(target=capture_stderr, daemon=True)
        
        stdout_thread.start()
        stderr_thread.start()
        
        self._output_threads[pid] = [stdout_thread, stderr_thread]
    
    def kill_process(self, pid: int, force: bool = False) -> bool:
        """Kill a process by PID."""
        try:
            with self._lock:
                if pid not in self.processes:
                    return False
                
                process_info = self.processes[pid]
                subprocess_handle = self.subprocess_handles.get(pid)
            
            if subprocess_handle:
                if force:
                    subprocess_handle.kill()
                else:
                    subprocess_handle.terminate()
                
                # Wait a bit for graceful shutdown
                try:
                    subprocess_handle.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    if not force:
                        subprocess_handle.kill()
                        subprocess_handle.wait()
            
            # Update process info
            with self._lock:
                process_info.state = ProcessState.KILLED
                process_info.end_time = time.time()
                if subprocess_handle:
                    process_info.return_code = subprocess_handle.returncode
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to kill process {pid}: {e}")
            return False
    
    def read_process_output(self, pid: int, lines: int = 50) -> Tuple[List[str], List[str]]:
        """Read recent output from a process."""
        with self._lock:
            if pid not in self.processes:
                return [], []
            
            process_info = self.processes[pid]
            stdout_lines = process_info.stdout_buffer[-lines:] if process_info.stdout_buffer else []
            stderr_lines = process_info.stderr_buffer[-lines:] if process_info.stderr_buffer else []
            
            return stdout_lines, stderr_lines
    
    def write_to_process(self, pid: int, input_data: str) -> bool:
        """Write input to a process."""
        try:
            with self._lock:
                subprocess_handle = self.subprocess_handles.get(pid)
            
            if not subprocess_handle or subprocess_handle.stdin is None:
                return False
            
            subprocess_handle.stdin.write(input_data + '\n')
            subprocess_handle.stdin.flush()
            return True
            
        except Exception as e:
            logger.error(f"Failed to write to process {pid}: {e}")
            return False
    
    def wait_for_process(self, pid: int, timeout: int = 60) -> Optional[int]:
        """Wait for a process to complete."""
        try:
            with self._lock:
                subprocess_handle = self.subprocess_handles.get(pid)
                process_info = self.processes.get(pid)
            
            if not subprocess_handle or not process_info:
                return None
            
            return_code = subprocess_handle.wait(timeout=timeout)
            
            with self._lock:
                process_info.return_code = return_code
                process_info.end_time = time.time()
                process_info.state = ProcessState.FINISHED
            
            return return_code
            
        except subprocess.TimeoutExpired:
            return None
        except Exception as e:
            logger.error(f"Error waiting for process {pid}: {e}")
            return None
    
    def list_processes(self) -> List[ProcessInfo]:
        """List all managed processes."""
        with self._lock:
            return list(self.processes.values())
    
    def get_process_info(self, pid: int) -> Optional[ProcessInfo]:
        """Get information about a specific process."""
        with self._lock:
            return self.processes.get(pid)
    
    def cleanup_finished_processes(self) -> int:
        """Clean up finished processes and return count."""
        cleaned_count = 0
        
        with self._lock:
            finished_pids = []
            
            for pid, process_info in self.processes.items():
                subprocess_handle = self.subprocess_handles.get(pid)
                
                if subprocess_handle and subprocess_handle.poll() is not None:
                    # Process has finished
                    process_info.return_code = subprocess_handle.returncode
                    process_info.end_time = time.time()
                    process_info.state = ProcessState.FINISHED
                    finished_pids.append(pid)
            
            # Remove old finished processes (keep last 10)
            all_finished = [pid for pid, info in self.processes.items() 
                          if info.state in [ProcessState.FINISHED, ProcessState.KILLED]]
            
            if len(all_finished) > 10:
                to_remove = sorted(all_finished, key=lambda p: self.processes[p].end_time or 0)[:-10]
                
                for pid in to_remove:
                    del self.processes[pid]
                    if pid in self.subprocess_handles:
                        del self.subprocess_handles[pid]
                    if pid in self._output_threads:
                        del self._output_threads[pid]
                    cleaned_count += 1
        
        return cleaned_count
    
    def get_system_processes(self) -> List[Dict[str, Any]]:
        """Get information about system processes."""
        try:
            processes = []
            
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent', 'status']):
                try:
                    processes.append(proc.info)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            return processes
            
        except Exception as e:
            logger.error(f"Error getting system processes: {e}")
            return []


class ProcessManagerTool(ToolSpec):
    """Advanced process management tool."""
    
    def __init__(self):
        super().__init__(
            name="process",
            description="Launch, manage, and interact with system processes",
            parameters=[
                Parameter(
                    name="action",
                    type="string",
                    description="Process action to perform",
                    required=True,
                    enum=["launch", "kill", "read", "write", "wait", "list", "info", "system", "cleanup"]
                ),
                Parameter(
                    name="command",
                    type="string",
                    description="Command to execute (for launch action)",
                    required=False
                ),
                Parameter(
                    name="pid",
                    type="integer",
                    description="Process ID for operations",
                    required=False
                ),
                Parameter(
                    name="input",
                    type="string",
                    description="Input to send to process",
                    required=False
                ),
                Parameter(
                    name="working_dir",
                    type="string",
                    description="Working directory for process",
                    required=False
                ),
                Parameter(
                    name="wait",
                    type="boolean",
                    description="Wait for process completion",
                    required=False,
                    default=False
                ),
                Parameter(
                    name="wait_timeout",
                    type="integer",
                    description="Timeout for waiting (seconds)",
                    required=False,
                    default=60
                ),
                Parameter(
                    name="force",
                    type="boolean",
                    description="Force kill process",
                    required=False,
                    default=False
                ),
                Parameter(
                    name="lines",
                    type="integer",
                    description="Number of output lines to read",
                    required=False,
                    default=50
                )
            ],
            block_types=["process", "proc", "launch", "kill"]
        )
        self.process_manager = ProcessManager()
    
    def is_available(self) -> bool:
        """Check if process management is available."""
        return True
    
    def execute(self, content: str, **kwargs) -> Generator[Message, None, None]:
        """
        Execute process management operation.
        
        Args:
            content: Operation content
            **kwargs: Additional parameters
        
        Yields:
            Response messages with operation results
        """
        try:
            # Parse content if action not specified
            if "action" not in kwargs:
                action, parsed_params = self._parse_content(content)
                kwargs.update({"action": action, **parsed_params})
            
            # Validate parameters
            params = self.validate_parameters(**kwargs)
            action = params["action"]
            
            # Execute action
            if action == "launch":
                yield from self._launch_process(params)
            elif action == "kill":
                yield from self._kill_process(params)
            elif action == "read":
                yield from self._read_process(params)
            elif action == "write":
                yield from self._write_process(params)
            elif action == "wait":
                yield from self._wait_process(params)
            elif action == "list":
                yield from self._list_processes()
            elif action == "info":
                yield from self._get_process_info(params)
            elif action == "system":
                yield from self._get_system_processes()
            elif action == "cleanup":
                yield from self._cleanup_processes()
            else:
                yield self.create_response(f"Unknown process action: {action}")
                
        except Exception as e:
            logger.error(f"Error in process operation: {e}")
            yield self.create_response(self.format_error(e))
    
    def _parse_content(self, content: str) -> Tuple[str, Dict[str, Any]]:
        """Parse content to extract action and parameters."""
        lines = content.strip().split('\n')
        
        if not lines:
            return "list", {}
        
        first_line = lines[0].strip()
        params = {}
        
        # Detect action from content
        if first_line.startswith("launch ") or first_line.startswith("run "):
            action = "launch"
            command = first_line.replace("launch ", "").replace("run ", "")
            params["command"] = command
        elif first_line.startswith("kill "):
            action = "kill"
            try:
                pid = int(first_line.replace("kill ", ""))
                params["pid"] = pid
            except ValueError:
                pass
        elif first_line.startswith("read "):
            action = "read"
            try:
                pid = int(first_line.replace("read ", ""))
                params["pid"] = pid
            except ValueError:
                pass
        elif first_line.startswith("write "):
            action = "write"
            parts = first_line.replace("write ", "").split(" ", 1)
            if len(parts) >= 2:
                try:
                    params["pid"] = int(parts[0])
                    params["input"] = parts[1]
                except ValueError:
                    pass
        elif "list" in first_line:
            action = "list"
        elif "system" in first_line:
            action = "system"
        elif "cleanup" in first_line:
            action = "cleanup"
        else:
            # Default to launch
            action = "launch"
            params["command"] = content
        
        return action, params
    
    def _launch_process(self, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Launch a new process."""
        command = params.get("command")
        
        if not command:
            yield self.create_response("Command is required to launch a process")
            return
        
        try:
            process_info = self.process_manager.launch_process(
                command=command,
                working_dir=params.get("working_dir"),
                wait=params.get("wait", False),
                wait_timeout=params.get("wait_timeout", 60)
            )
            
            response = f"🚀 Process launched successfully!\n"
            response += f"PID: {process_info.pid}\n"
            response += f"Command: {process_info.command}\n"
            response += f"State: {process_info.state.value}\n"
            
            if process_info.working_dir:
                response += f"Working Directory: {process_info.working_dir}\n"
            
            if process_info.return_code is not None:
                response += f"Return Code: {process_info.return_code}\n"
            
            # Show initial output if available
            stdout_lines, stderr_lines = self.process_manager.read_process_output(process_info.pid, 10)
            
            if stdout_lines:
                response += f"\nInitial Output:\n" + "\n".join(stdout_lines)
            
            if stderr_lines:
                response += f"\nInitial Errors:\n" + "\n".join(stderr_lines)
            
            yield self.create_response(response)
            
        except Exception as e:
            yield self.create_response(f"Failed to launch process: {e}")
    
    def _kill_process(self, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Kill a process."""
        pid = params.get("pid")
        
        if not pid:
            yield self.create_response("Process ID is required to kill a process")
            return
        
        force = params.get("force", False)
        success = self.process_manager.kill_process(pid, force)
        
        if success:
            yield self.create_response(f"✅ Process {pid} {'force killed' if force else 'terminated'} successfully")
        else:
            yield self.create_response(f"❌ Failed to kill process {pid} (process not found or already finished)")
    
    def _read_process(self, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Read output from a process."""
        pid = params.get("pid")
        
        if not pid:
            yield self.create_response("Process ID is required to read process output")
            return
        
        lines = params.get("lines", 50)
        stdout_lines, stderr_lines = self.process_manager.read_process_output(pid, lines)
        
        response = f"📖 Output from process {pid}:\n\n"
        
        if stdout_lines:
            response += "**STDOUT:**\n"
            response += "\n".join(stdout_lines) + "\n\n"
        
        if stderr_lines:
            response += "**STDERR:**\n"
            response += "\n".join(stderr_lines) + "\n\n"
        
        if not stdout_lines and not stderr_lines:
            response += "No output available"
        
        yield self.create_response(response)
    
    def _write_process(self, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Write input to a process."""
        pid = params.get("pid")
        input_data = params.get("input")
        
        if not pid:
            yield self.create_response("Process ID is required to write to process")
            return
        
        if not input_data:
            yield self.create_response("Input data is required")
            return
        
        success = self.process_manager.write_to_process(pid, input_data)
        
        if success:
            yield self.create_response(f"✅ Input sent to process {pid}")
        else:
            yield self.create_response(f"❌ Failed to send input to process {pid}")
    
    def _wait_process(self, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Wait for a process to complete."""
        pid = params.get("pid")
        
        if not pid:
            yield self.create_response("Process ID is required to wait for process")
            return
        
        timeout = params.get("wait_timeout", 60)
        return_code = self.process_manager.wait_for_process(pid, timeout)
        
        if return_code is not None:
            yield self.create_response(f"✅ Process {pid} completed with return code: {return_code}")
        else:
            yield self.create_response(f"⏱️ Process {pid} did not complete within {timeout} seconds")
    
    def _list_processes(self) -> Generator[Message, None, None]:
        """List all managed processes."""
        processes = self.process_manager.list_processes()
        
        if not processes:
            yield self.create_response("No managed processes found")
            return
        
        response = f"📋 Managed Processes ({len(processes)}):\n\n"
        
        for process in processes:
            response += f"**PID {process.pid}**: {process.command[:50]}{'...' if len(process.command) > 50 else ''}\n"
            response += f"   State: {process.state.value}\n"
            response += f"   Started: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(process.start_time))}\n"
            
            if process.return_code is not None:
                response += f"   Return Code: {process.return_code}\n"
            
            if process.working_dir:
                response += f"   Working Dir: {process.working_dir}\n"
            
            response += "\n"
        
        yield self.create_response(response)
    
    def _get_process_info(self, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Get detailed information about a process."""
        pid = params.get("pid")
        
        if not pid:
            yield self.create_response("Process ID is required to get process info")
            return
        
        process_info = self.process_manager.get_process_info(pid)
        
        if not process_info:
            yield self.create_response(f"Process {pid} not found")
            return
        
        response = f"ℹ️ Process {pid} Information:\n\n"
        response += f"Command: {process_info.command}\n"
        response += f"State: {process_info.state.value}\n"
        response += f"Started: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(process_info.start_time))}\n"
        
        if process_info.end_time:
            response += f"Ended: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(process_info.end_time))}\n"
            duration = process_info.end_time - process_info.start_time
            response += f"Duration: {duration:.2f} seconds\n"
        
        if process_info.return_code is not None:
            response += f"Return Code: {process_info.return_code}\n"
        
        if process_info.working_dir:
            response += f"Working Directory: {process_info.working_dir}\n"
        
        # Show recent output
        stdout_lines, stderr_lines = self.process_manager.read_process_output(pid, 20)
        
        if stdout_lines:
            response += f"\nRecent STDOUT ({len(stdout_lines)} lines):\n"
            response += "\n".join(stdout_lines[-10:]) + "\n"
        
        if stderr_lines:
            response += f"\nRecent STDERR ({len(stderr_lines)} lines):\n"
            response += "\n".join(stderr_lines[-10:]) + "\n"
        
        yield self.create_response(response)
    
    def _get_system_processes(self) -> Generator[Message, None, None]:
        """Get information about system processes."""
        try:
            processes = self.process_manager.get_system_processes()
            
            if not processes:
                yield self.create_response("No system processes found")
                return
            
            # Sort by CPU usage
            processes.sort(key=lambda p: p.get('cpu_percent', 0), reverse=True)
            
            response = f"🖥️ System Processes (Top 20 by CPU usage):\n\n"
            
            for i, proc in enumerate(processes[:20], 1):
                response += f"{i:2d}. PID {proc['pid']:>6} | {proc['name']:<20} | "
                response += f"CPU: {proc.get('cpu_percent', 0):>5.1f}% | "
                response += f"MEM: {proc.get('memory_percent', 0):>5.1f}% | "
                response += f"Status: {proc.get('status', 'unknown')}\n"
            
            yield self.create_response(response)
            
        except Exception as e:
            yield self.create_response(f"Error getting system processes: {e}")
    
    def _cleanup_processes(self) -> Generator[Message, None, None]:
        """Clean up finished processes."""
        cleaned_count = self.process_manager.cleanup_finished_processes()
        
        if cleaned_count > 0:
            yield self.create_response(f"🧹 Cleaned up {cleaned_count} finished processes")
        else:
            yield self.create_response("No processes needed cleanup")


# Create tool instance
process_manager_tool = ProcessManagerTool()
