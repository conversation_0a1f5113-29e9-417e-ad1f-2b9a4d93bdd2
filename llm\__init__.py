"""
LLM (Large Language Model) providers and utilities.

This module provides interfaces for different LLM providers,
with primary support for Google's Gemini 2.0 Flash model.
"""

import logging
from typing import List, Dict, Any, Optional, Generator, Union
from message import Message
from config import get_config
from llm.gemini_provider import GeminiProvider
from llm.models import ModelInfo, get_model_info

logger = logging.getLogger(__name__)

# Global provider instance
_provider: Optional[GeminiProvider] = None


def get_provider() -> GeminiProvider:
    """Get the global LLM provider instance."""
    global _provider
    if _provider is None:
        config = get_config()
        _provider = GeminiProvider(
            api_key=config.llm.api_key,
            model=config.llm.model,
            temperature=config.llm.temperature,
            max_tokens=config.llm.max_tokens,
            top_p=config.llm.top_p,
            top_k=config.llm.top_k,
            timeout=config.llm.timeout,
            max_retries=config.llm.max_retries
        )
    return _provider


def set_provider(provider: GeminiProvider) -> None:
    """Set the global LLM provider instance."""
    global _provider
    _provider = provider


def reply(
    messages: List[Message],
    model: Optional[str] = None,
    stream: bool = True,
    tools: Optional[List[Dict[str, Any]]] = None,
    **kwargs
) -> Union[Message, Generator[Message, None, None]]:
    """
    Generate a reply from the LLM.
    
    Args:
        messages: List of conversation messages
        model: Model name to use (optional)
        stream: Whether to stream the response
        tools: Available tools for function calling
        **kwargs: Additional parameters
    
    Returns:
        Message or generator of Messages if streaming
    """
    provider = get_provider()
    
    if model:
        provider.model = model
    
    try:
        if stream:
            return provider.stream_reply(messages, tools=tools, **kwargs)
        else:
            return provider.reply(messages, tools=tools, **kwargs)
    except Exception as e:
        logger.error(f"Error generating reply: {e}")
        raise


def get_model(model_name: Optional[str] = None) -> ModelInfo:
    """
    Get model information.
    
    Args:
        model_name: Name of the model (optional)
    
    Returns:
        ModelInfo object with model details
    """
    if model_name is None:
        config = get_config()
        model_name = config.llm.model
    
    return get_model_info(model_name)


def list_available_models() -> List[str]:
    """List all available models."""
    provider = get_provider()
    return provider.list_models()


def estimate_tokens(text: str, model: Optional[str] = None) -> int:
    """
    Estimate token count for text.
    
    Args:
        text: Text to estimate tokens for
        model: Model name (optional)
    
    Returns:
        Estimated token count
    """
    provider = get_provider()
    if model:
        provider.model = model
    return provider.estimate_tokens(text)


def estimate_cost(messages: List[Message], model: Optional[str] = None) -> float:
    """
    Estimate cost for messages.
    
    Args:
        messages: List of messages
        model: Model name (optional)
    
    Returns:
        Estimated cost in USD
    """
    provider = get_provider()
    if model:
        provider.model = model
    return provider.estimate_cost(messages)


__all__ = [
    "get_provider",
    "set_provider", 
    "reply",
    "get_model",
    "list_available_models",
    "estimate_tokens",
    "estimate_cost",
    "GeminiProvider",
    "ModelInfo",
    "get_model_info",
]
