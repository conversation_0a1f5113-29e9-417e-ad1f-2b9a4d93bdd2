"""
Base classes and utilities for the tools system.

This module provides the foundation for all tools in the AI agent,
including the ToolSpec base class and utility functions.
"""

import re
import uuid
import logging
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional, Generator, Callable, Union
from enum import Enum

from message import Message

logger = logging.getLogger(__name__)


@dataclass
class Parameter:
    """Tool parameter definition."""
    name: str
    type: str
    description: str
    required: bool = True
    default: Any = None
    enum: Optional[List[str]] = None


class ConfirmFunc:
    """Type alias for confirmation function."""
    def __call__(self, message: str) -> bool:
        """Confirm an action with the user."""
        ...


@dataclass
class ToolUse:
    """Represents a tool use/call extracted from content."""
    tool: str
    content: str
    call_id: Optional[str] = None
    kwargs: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def is_runnable(self) -> bool:
        """Check if this tool use can be executed."""
        return bool(self.tool and self.content)
    
    @classmethod
    def iter_from_content(cls, content: str) -> Generator['ToolUse', None, None]:
        """
        Extract tool uses from content.
        
        Supports multiple formats:
        - Markdown code blocks: ```tool_name\ncontent```
        - XML tags: <tool_name>content</tool_name>
        - Function calls: tool_name(args)
        """
        # Markdown code blocks
        markdown_pattern = r'```(\w+)(?:\s+([^\n]*))?\n(.*?)```'
        for match in re.finditer(markdown_pattern, content, re.DOTALL):
            tool_name = match.group(1)
            args_str = match.group(2) or ""
            tool_content = match.group(3).strip()
            
            # Parse arguments
            kwargs = {}
            if args_str:
                # Simple key=value parsing
                for arg in args_str.split():
                    if '=' in arg:
                        key, value = arg.split('=', 1)
                        kwargs[key] = value
            
            yield cls(
                tool=tool_name,
                content=tool_content,
                call_id=str(uuid.uuid4()),
                kwargs=kwargs
            )
        
        # XML tags
        xml_pattern = r'<(\w+)(?:\s+([^>]*))?>([^<]*)</\1>'
        for match in re.finditer(xml_pattern, content, re.DOTALL):
            tool_name = match.group(1)
            attrs_str = match.group(2) or ""
            tool_content = match.group(3).strip()
            
            # Parse attributes
            kwargs = {}
            if attrs_str:
                attr_pattern = r'(\w+)="([^"]*)"'
                for attr_match in re.finditer(attr_pattern, attrs_str):
                    kwargs[attr_match.group(1)] = attr_match.group(2)
            
            yield cls(
                tool=tool_name,
                content=tool_content,
                call_id=str(uuid.uuid4()),
                kwargs=kwargs
            )


class ToolSpec(ABC):
    """
    Base class for all tools.
    
    Tools provide specific capabilities to the AI agent,
    such as executing shell commands, running Python code,
    browsing the web, etc.
    """
    
    def __init__(
        self,
        name: str,
        description: str,
        parameters: Optional[List[Parameter]] = None,
        block_types: Optional[List[str]] = None,
        is_runnable: bool = True,
        disabled_by_default: bool = False
    ):
        """
        Initialize a tool.
        
        Args:
            name: Tool name
            description: Tool description
            parameters: List of tool parameters
            block_types: List of block types this tool handles
            is_runnable: Whether this tool can be executed
            disabled_by_default: Whether this tool is disabled by default
        """
        self.name = name
        self.description = description
        self.parameters = parameters or []
        self.block_types = block_types or [name]
        self.is_runnable = is_runnable
        self.disabled_by_default = disabled_by_default
        self._initialized = False
    
    @abstractmethod
    def is_available(self) -> bool:
        """Check if the tool is available for use."""
        pass
    
    @abstractmethod
    def execute(self, content: str, **kwargs) -> Generator[Message, None, None]:
        """
        Execute the tool with given content.
        
        Args:
            content: Tool input content
            **kwargs: Additional keyword arguments
        
        Yields:
            Response messages
        """
        pass
    
    def init(self) -> 'ToolSpec':
        """
        Initialize the tool (called once when tool is loaded).
        
        Returns:
            The initialized tool instance
        """
        if not self._initialized:
            self._do_init()
            self._initialized = True
        return self
    
    def _do_init(self) -> None:
        """Override this method to perform tool-specific initialization."""
        pass
    
    def validate_parameters(self, **kwargs) -> Dict[str, Any]:
        """
        Validate and process parameters.
        
        Args:
            **kwargs: Parameter values
        
        Returns:
            Validated parameters
        
        Raises:
            ValueError: If required parameters are missing or invalid
        """
        validated = {}
        
        for param in self.parameters:
            value = kwargs.get(param.name, param.default)
            
            if param.required and value is None:
                raise ValueError(f"Required parameter '{param.name}' is missing")
            
            if value is not None:
                # Type validation
                if param.type == "string" and not isinstance(value, str):
                    value = str(value)
                elif param.type == "integer" and not isinstance(value, int):
                    try:
                        value = int(value)
                    except ValueError:
                        raise ValueError(f"Parameter '{param.name}' must be an integer")
                elif param.type == "number" and not isinstance(value, (int, float)):
                    try:
                        value = float(value)
                    except ValueError:
                        raise ValueError(f"Parameter '{param.name}' must be a number")
                elif param.type == "boolean" and not isinstance(value, bool):
                    if isinstance(value, str):
                        value = value.lower() in ('true', '1', 'yes', 'on')
                    else:
                        value = bool(value)
                
                # Enum validation
                if param.enum and value not in param.enum:
                    raise ValueError(f"Parameter '{param.name}' must be one of: {param.enum}")
            
            validated[param.name] = value
        
        return validated
    
    def format_error(self, error: Exception) -> str:
        """Format an error message for display."""
        return f"Error in {self.name}: {str(error)}"
    
    def create_response(self, content: str, **metadata) -> Message:
        """Create a response message."""
        return Message(
            role="system",
            content=content,
            metadata={
                "tool": self.name,
                **metadata
            }
        )
    
    def __str__(self) -> str:
        """String representation of the tool."""
        return f"Tool({self.name})"
    
    def __repr__(self) -> str:
        """Detailed string representation."""
        return (f"ToolSpec(name='{self.name}', description='{self.description}', "
                f"is_runnable={self.is_runnable}, available={self.is_available()})")


class SimpleToolSpec(ToolSpec):
    """
    Simple tool implementation for basic tools.
    
    This class provides a simplified way to create tools
    by just providing an execute function.
    """
    
    def __init__(
        self,
        name: str,
        description: str,
        execute_func: Callable[[str], Union[str, Generator[Message, None, None]]],
        is_available_func: Optional[Callable[[], bool]] = None,
        **kwargs
    ):
        """
        Initialize a simple tool.
        
        Args:
            name: Tool name
            description: Tool description
            execute_func: Function to execute the tool
            is_available_func: Function to check availability
            **kwargs: Additional ToolSpec arguments
        """
        super().__init__(name, description, **kwargs)
        self._execute_func = execute_func
        self._is_available_func = is_available_func or (lambda: True)
    
    def is_available(self) -> bool:
        """Check if the tool is available."""
        try:
            return self._is_available_func()
        except Exception:
            return False
    
    def execute(self, content: str, **kwargs) -> Generator[Message, None, None]:
        """Execute the tool."""
        try:
            result = self._execute_func(content)
            
            if isinstance(result, str):
                yield self.create_response(result)
            elif hasattr(result, '__iter__'):
                yield from result
            else:
                yield self.create_response(str(result))
                
        except Exception as e:
            logger.error(f"Error executing {self.name}: {e}")
            yield self.create_response(self.format_error(e))


def create_simple_tool(
    name: str,
    description: str,
    execute_func: Callable[[str], Union[str, Generator[Message, None, None]]],
    **kwargs
) -> SimpleToolSpec:
    """
    Create a simple tool.
    
    Args:
        name: Tool name
        description: Tool description
        execute_func: Function to execute the tool
        **kwargs: Additional arguments
    
    Returns:
        SimpleToolSpec instance
    """
    return SimpleToolSpec(name, description, execute_func, **kwargs)
