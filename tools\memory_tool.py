"""
Advanced memory management tool for persistent context and learning.

This tool provides intelligent memory storage, retrieval, and management
capabilities for the AI agent to maintain context and learn from interactions.
"""

import json
import logging
import sqlite3
import hashlib
import time
from datetime import datetime, timedelta
from typing import Generator, Dict, Any, List, Optional, Tuple
from pathlib import Path
from dataclasses import dataclass, asdict
from enum import Enum

from tools.base import ToolSpec, Parameter
from message import Message
from config import get_config

logger = logging.getLogger(__name__)


class MemoryType(Enum):
    """Types of memories that can be stored."""
    PROJECT = "project"
    USER_PREFERENCE = "user_preference"
    CODE_PATTERN = "code_pattern"
    ERROR_SOLUTION = "error_solution"
    WORKFLOW = "workflow"
    DECISION = "decision"
    LEARNING = "learning"
    CONTEXT = "context"


@dataclass
class Memory:
    """Represents a single memory entry."""
    id: str
    type: MemoryType
    title: str
    content: str
    tags: List[str]
    importance: float  # 0.0 to 1.0
    created_at: datetime
    last_accessed: datetime
    access_count: int
    project_path: Optional[str] = None
    metadata: Dict[str, Any] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert memory to dictionary."""
        return {
            'id': self.id,
            'type': self.type.value,
            'title': self.title,
            'content': self.content,
            'tags': self.tags,
            'importance': self.importance,
            'created_at': self.created_at.isoformat(),
            'last_accessed': self.last_accessed.isoformat(),
            'access_count': self.access_count,
            'project_path': self.project_path,
            'metadata': self.metadata or {}
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Memory':
        """Create memory from dictionary."""
        return cls(
            id=data['id'],
            type=MemoryType(data['type']),
            title=data['title'],
            content=data['content'],
            tags=data['tags'],
            importance=data['importance'],
            created_at=datetime.fromisoformat(data['created_at']),
            last_accessed=datetime.fromisoformat(data['last_accessed']),
            access_count=data['access_count'],
            project_path=data.get('project_path'),
            metadata=data.get('metadata', {})
        )


class MemoryManager:
    """Advanced memory management system."""
    
    def __init__(self, db_path: Optional[Path] = None):
        """Initialize memory manager."""
        config = get_config()
        self.db_path = db_path or (config.data_dir / "memories.db")
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        self._init_database()
    
    def _init_database(self) -> None:
        """Initialize the SQLite database."""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS memories (
                    id TEXT PRIMARY KEY,
                    type TEXT NOT NULL,
                    title TEXT NOT NULL,
                    content TEXT NOT NULL,
                    tags TEXT NOT NULL,
                    importance REAL NOT NULL,
                    created_at TEXT NOT NULL,
                    last_accessed TEXT NOT NULL,
                    access_count INTEGER NOT NULL,
                    project_path TEXT,
                    metadata TEXT
                )
            """)
            
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_type ON memories(type)
            """)
            
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_importance ON memories(importance)
            """)
            
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_project_path ON memories(project_path)
            """)
            
            conn.execute("""
                CREATE VIRTUAL TABLE IF NOT EXISTS memories_fts USING fts5(
                    id, title, content, tags, content=memories
                )
            """)
    
    def store_memory(self, memory: Memory) -> None:
        """Store a memory in the database."""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT OR REPLACE INTO memories 
                (id, type, title, content, tags, importance, created_at, 
                 last_accessed, access_count, project_path, metadata)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                memory.id,
                memory.type.value,
                memory.title,
                memory.content,
                json.dumps(memory.tags),
                memory.importance,
                memory.created_at.isoformat(),
                memory.last_accessed.isoformat(),
                memory.access_count,
                memory.project_path,
                json.dumps(memory.metadata or {})
            ))
            
            # Update FTS index
            conn.execute("""
                INSERT OR REPLACE INTO memories_fts 
                (id, title, content, tags)
                VALUES (?, ?, ?, ?)
            """, (
                memory.id,
                memory.title,
                memory.content,
                ' '.join(memory.tags)
            ))
    
    def retrieve_memories(
        self,
        query: Optional[str] = None,
        memory_type: Optional[MemoryType] = None,
        project_path: Optional[str] = None,
        tags: Optional[List[str]] = None,
        limit: int = 10,
        min_importance: float = 0.0
    ) -> List[Memory]:
        """Retrieve memories based on criteria."""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            
            if query:
                # Use FTS for text search
                sql = """
                    SELECT m.* FROM memories m
                    JOIN memories_fts fts ON m.id = fts.id
                    WHERE fts MATCH ?
                """
                params = [query]
            else:
                sql = "SELECT * FROM memories WHERE 1=1"
                params = []
            
            if memory_type:
                sql += " AND type = ?"
                params.append(memory_type.value)
            
            if project_path:
                sql += " AND project_path = ?"
                params.append(project_path)
            
            if min_importance > 0:
                sql += " AND importance >= ?"
                params.append(min_importance)
            
            sql += " ORDER BY importance DESC, last_accessed DESC LIMIT ?"
            params.append(limit)
            
            cursor = conn.execute(sql, params)
            rows = cursor.fetchall()
            
            memories = []
            for row in rows:
                memory_data = dict(row)
                memory_data['tags'] = json.loads(memory_data['tags'])
                memory_data['metadata'] = json.loads(memory_data['metadata'] or '{}')
                memories.append(Memory.from_dict(memory_data))
            
            # Update access count and time
            for memory in memories:
                memory.access_count += 1
                memory.last_accessed = datetime.now()
                self.store_memory(memory)
            
            return memories
    
    def delete_memory(self, memory_id: str) -> bool:
        """Delete a memory by ID."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("DELETE FROM memories WHERE id = ?", (memory_id,))
            conn.execute("DELETE FROM memories_fts WHERE id = ?", (memory_id,))
            return cursor.rowcount > 0
    
    def compress_memories(self, max_memories: int = 1000) -> int:
        """Compress old, low-importance memories."""
        with sqlite3.connect(self.db_path) as conn:
            # Get count of memories
            cursor = conn.execute("SELECT COUNT(*) FROM memories")
            total_memories = cursor.fetchone()[0]
            
            if total_memories <= max_memories:
                return 0
            
            # Delete oldest, least important memories
            memories_to_delete = total_memories - max_memories
            conn.execute("""
                DELETE FROM memories WHERE id IN (
                    SELECT id FROM memories 
                    ORDER BY importance ASC, last_accessed ASC 
                    LIMIT ?
                )
            """, (memories_to_delete,))
            
            return memories_to_delete
    
    def get_memory_stats(self) -> Dict[str, Any]:
        """Get memory statistics."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("SELECT COUNT(*) FROM memories")
            total_memories = cursor.fetchone()[0]
            
            cursor = conn.execute("""
                SELECT type, COUNT(*) FROM memories GROUP BY type
            """)
            type_counts = dict(cursor.fetchall())
            
            cursor = conn.execute("""
                SELECT AVG(importance) FROM memories
            """)
            avg_importance = cursor.fetchone()[0] or 0.0
            
            return {
                'total_memories': total_memories,
                'type_counts': type_counts,
                'average_importance': avg_importance
            }


class MemoryToolSpec(ToolSpec):
    """Advanced memory management tool."""
    
    def __init__(self):
        super().__init__(
            name="memory",
            description="Store, retrieve, and manage persistent memories for context and learning",
            parameters=[
                Parameter(
                    name="action",
                    type="string",
                    description="Memory action to perform",
                    required=True,
                    enum=["store", "retrieve", "search", "delete", "stats", "compress", "learn"]
                ),
                Parameter(
                    name="content",
                    type="string",
                    description="Memory content or search query",
                    required=False
                ),
                Parameter(
                    name="title",
                    type="string",
                    description="Memory title",
                    required=False
                ),
                Parameter(
                    name="type",
                    type="string",
                    description="Memory type",
                    required=False,
                    enum=["project", "user_preference", "code_pattern", "error_solution", "workflow", "decision", "learning", "context"]
                ),
                Parameter(
                    name="tags",
                    type="string",
                    description="Comma-separated tags",
                    required=False
                ),
                Parameter(
                    name="importance",
                    type="number",
                    description="Memory importance (0.0 to 1.0)",
                    required=False,
                    default=0.5
                ),
                Parameter(
                    name="project_path",
                    type="string",
                    description="Associated project path",
                    required=False
                )
            ],
            block_types=["memory", "remember", "recall", "learn"]
        )
        self.memory_manager = MemoryManager()
    
    def is_available(self) -> bool:
        """Check if memory tool is available."""
        return True
    
    def execute(self, content: str, **kwargs) -> Generator[Message, None, None]:
        """
        Execute memory operation.
        
        Args:
            content: Operation content
            **kwargs: Additional parameters
        
        Yields:
            Response messages with operation results
        """
        try:
            # Parse content if action not specified
            if "action" not in kwargs:
                action, parsed_content = self._parse_content(content)
                kwargs.update({
                    "action": action,
                    "content": parsed_content
                })
            
            # Validate parameters
            params = self.validate_parameters(**kwargs)
            action = params["action"]
            
            # Execute action
            if action == "store":
                yield from self._store_memory(params)
            elif action == "retrieve":
                yield from self._retrieve_memories(params)
            elif action == "search":
                yield from self._search_memories(params)
            elif action == "delete":
                yield from self._delete_memory(params)
            elif action == "stats":
                yield from self._get_stats()
            elif action == "compress":
                yield from self._compress_memories()
            elif action == "learn":
                yield from self._learn_from_interaction(params)
            else:
                yield self.create_response(f"Unknown memory action: {action}")
                
        except Exception as e:
            logger.error(f"Error in memory operation: {e}")
            yield self.create_response(self.format_error(e))
    
    def _parse_content(self, content: str) -> Tuple[str, str]:
        """Parse content to extract action and content."""
        lines = content.strip().split('\n')
        
        if not lines:
            return "retrieve", ""
        
        first_line = lines[0].strip().lower()
        
        # Detect action from content
        if any(word in first_line for word in ["remember", "store", "save"]):
            action = "store"
            content_text = '\n'.join(lines[1:]) if len(lines) > 1 else first_line
        elif any(word in first_line for word in ["search", "find", "look"]):
            action = "search"
            content_text = first_line.replace("search", "").replace("find", "").replace("look", "").strip()
        elif any(word in first_line for word in ["recall", "retrieve", "get"]):
            action = "retrieve"
            content_text = first_line.replace("recall", "").replace("retrieve", "").replace("get", "").strip()
        elif "delete" in first_line:
            action = "delete"
            content_text = first_line.replace("delete", "").strip()
        elif "stats" in first_line:
            action = "stats"
            content_text = ""
        else:
            action = "store"  # Default to storing
            content_text = content
        
        return action, content_text
    
    def _store_memory(self, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Store a new memory."""
        content = params.get("content", "")
        title = params.get("title", content[:50] + "..." if len(content) > 50 else content)
        memory_type = MemoryType(params.get("type", "context"))
        tags = [tag.strip() for tag in params.get("tags", "").split(",") if tag.strip()]
        importance = float(params.get("importance", 0.5))
        project_path = params.get("project_path")
        
        if not content:
            yield self.create_response("Content is required to store a memory")
            return
        
        # Generate memory ID
        memory_id = hashlib.md5(f"{content}{title}{time.time()}".encode()).hexdigest()
        
        # Create memory
        memory = Memory(
            id=memory_id,
            type=memory_type,
            title=title,
            content=content,
            tags=tags,
            importance=importance,
            created_at=datetime.now(),
            last_accessed=datetime.now(),
            access_count=0,
            project_path=project_path,
            metadata={}
        )
        
        # Store memory
        self.memory_manager.store_memory(memory)
        
        yield self.create_response(
            f"✅ Memory stored successfully!\n"
            f"ID: {memory_id}\n"
            f"Title: {title}\n"
            f"Type: {memory_type.value}\n"
            f"Tags: {', '.join(tags) if tags else 'None'}\n"
            f"Importance: {importance}"
        )
    
    def _retrieve_memories(self, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Retrieve memories based on criteria."""
        memory_type = MemoryType(params["type"]) if params.get("type") else None
        project_path = params.get("project_path")
        tags = [tag.strip() for tag in params.get("tags", "").split(",") if tag.strip()]
        
        memories = self.memory_manager.retrieve_memories(
            memory_type=memory_type,
            project_path=project_path,
            tags=tags if tags else None,
            limit=20
        )
        
        if not memories:
            yield self.create_response("No memories found matching the criteria")
            return
        
        response = f"📚 Found {len(memories)} memories:\n\n"
        
        for i, memory in enumerate(memories, 1):
            response += f"**{i}. {memory.title}**\n"
            response += f"   Type: {memory.type.value}\n"
            response += f"   Importance: {memory.importance:.2f}\n"
            response += f"   Tags: {', '.join(memory.tags) if memory.tags else 'None'}\n"
            response += f"   Content: {memory.content[:100]}{'...' if len(memory.content) > 100 else ''}\n"
            response += f"   Created: {memory.created_at.strftime('%Y-%m-%d %H:%M')}\n\n"
        
        yield self.create_response(response)
    
    def _search_memories(self, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Search memories using full-text search."""
        query = params.get("content", "")
        
        if not query:
            yield self.create_response("Search query is required")
            return
        
        memories = self.memory_manager.retrieve_memories(
            query=query,
            limit=15
        )
        
        if not memories:
            yield self.create_response(f"No memories found for query: '{query}'")
            return
        
        response = f"🔍 Search results for '{query}' ({len(memories)} found):\n\n"
        
        for i, memory in enumerate(memories, 1):
            response += f"**{i}. {memory.title}**\n"
            response += f"   Type: {memory.type.value} | Importance: {memory.importance:.2f}\n"
            response += f"   Content: {memory.content[:150]}{'...' if len(memory.content) > 150 else ''}\n"
            response += f"   Tags: {', '.join(memory.tags) if memory.tags else 'None'}\n\n"
        
        yield self.create_response(response)
    
    def _delete_memory(self, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Delete a memory by ID."""
        content = params.get("content", "")
        
        if not content:
            yield self.create_response("Memory ID is required for deletion")
            return
        
        success = self.memory_manager.delete_memory(content)
        
        if success:
            yield self.create_response(f"✅ Memory {content} deleted successfully")
        else:
            yield self.create_response(f"❌ Memory {content} not found")
    
    def _get_stats(self) -> Generator[Message, None, None]:
        """Get memory statistics."""
        stats = self.memory_manager.get_memory_stats()
        
        response = "📊 Memory Statistics:\n\n"
        response += f"Total Memories: {stats['total_memories']}\n"
        response += f"Average Importance: {stats['average_importance']:.2f}\n\n"
        
        response += "Memory Types:\n"
        for mem_type, count in stats['type_counts'].items():
            response += f"  • {mem_type}: {count}\n"
        
        yield self.create_response(response)
    
    def _compress_memories(self) -> Generator[Message, None, None]:
        """Compress old memories."""
        deleted_count = self.memory_manager.compress_memories()
        
        if deleted_count > 0:
            yield self.create_response(f"🗜️ Compressed {deleted_count} old memories")
        else:
            yield self.create_response("No memories needed compression")
    
    def _learn_from_interaction(self, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Learn from user interaction and store insights."""
        content = params.get("content", "")
        
        if not content:
            yield self.create_response("Learning content is required")
            return
        
        # Analyze content for learning opportunities
        insights = self._extract_insights(content)
        
        for insight in insights:
            memory = Memory(
                id=hashlib.md5(f"{insight['content']}{time.time()}".encode()).hexdigest(),
                type=MemoryType.LEARNING,
                title=insight['title'],
                content=insight['content'],
                tags=insight['tags'],
                importance=insight['importance'],
                created_at=datetime.now(),
                last_accessed=datetime.now(),
                access_count=0,
                metadata={'source': 'interaction_learning'}
            )
            
            self.memory_manager.store_memory(memory)
        
        yield self.create_response(f"🧠 Learned {len(insights)} insights from interaction")
    
    def _extract_insights(self, content: str) -> List[Dict[str, Any]]:
        """Extract learning insights from content."""
        insights = []
        
        # Simple pattern-based insight extraction
        if "error" in content.lower() and "solution" in content.lower():
            insights.append({
                'title': 'Error Solution Pattern',
                'content': content,
                'tags': ['error', 'solution', 'debugging'],
                'importance': 0.8
            })
        
        if any(tech in content.lower() for tech in ['react', 'node', 'python', 'javascript']):
            insights.append({
                'title': 'Technology Usage Pattern',
                'content': content,
                'tags': ['technology', 'development', 'pattern'],
                'importance': 0.6
            })
        
        if "prefer" in content.lower() or "like" in content.lower():
            insights.append({
                'title': 'User Preference',
                'content': content,
                'tags': ['preference', 'user', 'style'],
                'importance': 0.7
            })
        
        return insights


# Create tool instance
memory_tool = MemoryToolSpec()
