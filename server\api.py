"""
REST API endpoints for the AI agent server.

This module provides REST API endpoints for interacting with
the AI agent programmatically.
"""

try:
    from flask import Blueprint, request, jsonify
    FLASK_AVAILABLE = True
except ImportError:
    FLASK_AVAILABLE = False

from chat import generate_response
from message import Message, create_user_message
from tools import init_tools, get_tools_for_llm
from conversation import ConversationManager, list_conversations
from config import get_config

if FLASK_AVAILABLE:
    api_bp = Blueprint('api', __name__)
else:
    # Placeholder for when Flask is not available
    class MockBlueprint:
        def route(self, *args, **kwargs):
            def decorator(f):
                return f
            return decorator
    
    api_bp = MockBlueprint()

@api_bp.route('/health', methods=['GET'])
def health():
    """Health check endpoint."""
    return jsonify({"status": "healthy", "service": "Advanced AI Agent"})

@api_bp.route('/chat', methods=['POST'])
def chat():
    """
    Chat endpoint for sending messages to the AI agent.
    
    Expected JSON payload:
    {
        "message": "User message",
        "conversation_id": "optional-conversation-id",
        "model": "optional-model-name",
        "stream": true/false
    }
    """
    try:
        data = request.get_json()
        
        if not data or 'message' not in data:
            return jsonify({"error": "Message is required"}), 400
        
        user_message = data['message']
        conversation_id = data.get('conversation_id')
        model = data.get('model')
        stream = data.get('stream', False)
        
        # Get or create conversation
        conversation = ConversationManager(conversation_id)
        
        # Add user message
        user_msg = create_user_message(user_message)
        conversation.add_message(user_msg)
        
        # Initialize tools
        tools = init_tools()
        
        # Generate response
        response_generator = generate_response(
            conversation.messages,
            model=model,
            stream=stream,
            tools=get_tools_for_llm()
        )
        
        if stream:
            # TODO: Implement streaming response
            responses = list(response_generator)
            assistant_msg = responses[-1] if responses else Message("assistant", "No response")
        else:
            assistant_msg = next(response_generator)
        
        # Add assistant message to conversation
        conversation.add_message(assistant_msg)
        conversation.save()
        
        return jsonify({
            "response": assistant_msg.content,
            "conversation_id": conversation.conversation_id,
            "message_count": len(conversation.messages)
        })
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@api_bp.route('/conversations', methods=['GET'])
def get_conversations():
    """Get list of all conversations."""
    try:
        conversations = list_conversations()
        return jsonify({"conversations": conversations})
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@api_bp.route('/conversations/<conversation_id>', methods=['GET'])
def get_conversation(conversation_id):
    """Get a specific conversation."""
    try:
        conversation = ConversationManager(conversation_id)
        
        if not conversation.messages:
            return jsonify({"error": "Conversation not found"}), 404
        
        return jsonify({
            "conversation_id": conversation.conversation_id,
            "metadata": conversation.metadata,
            "messages": [msg.to_dict() for msg in conversation.messages],
            "stats": conversation.get_conversation_stats()
        })
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@api_bp.route('/conversations/<conversation_id>', methods=['DELETE'])
def delete_conversation(conversation_id):
    """Delete a conversation."""
    try:
        conversation = ConversationManager(conversation_id)
        
        if conversation.conversation_file.exists():
            conversation.conversation_file.unlink()
            return jsonify({"message": "Conversation deleted"})
        else:
            return jsonify({"error": "Conversation not found"}), 404
            
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@api_bp.route('/tools', methods=['GET'])
def get_tools():
    """Get list of available tools."""
    try:
        tools = init_tools()
        
        tool_info = []
        for tool in tools:
            tool_info.append({
                "name": tool.name,
                "description": tool.description,
                "is_available": tool.is_available(),
                "is_runnable": tool.is_runnable,
                "block_types": tool.block_types
            })
        
        return jsonify({"tools": tool_info})
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@api_bp.route('/config', methods=['GET'])
def get_config_info():
    """Get configuration information."""
    try:
        config = get_config()
        
        config_info = {
            "model": config.llm.model,
            "temperature": config.llm.temperature,
            "max_tokens": config.llm.max_tokens,
            "enabled_tools": config.tools.enabled,
            "auto_confirm": config.tools.auto_confirm,
            "features": {
                "tts_enabled": config.features.tts_enabled,
                "telemetry_enabled": config.features.telemetry_enabled,
                "auto_commit": config.features.auto_commit
            }
        }
        
        return jsonify({"config": config_info})
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500

__all__ = ["api_bp"]
