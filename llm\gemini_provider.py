"""
Gemini LLM Provider for Google's Gemini models.

This module provides the main interface for interacting with Google's
Gemini 2.0 Flash and other Gemini models.
"""

import os
import json
import logging
import time
from typing import List, Dict, Any, Optional, Generator, Union
from pathlib import Path

try:
    import google.generativeai as genai
    from google.generativeai.types import Harm<PERSON>ategory, HarmBlockThreshold
    from google.ai.generativelanguage_v1beta.types import content
except ImportError:
    raise ImportError(
        "Google Generative AI library not found. "
        "Install with: pip install google-generativeai"
    )

from message import Message
from llm.models import get_model_info, ModelInfo, estimate_tokens_simple

logger = logging.getLogger(__name__)


class GeminiProvider:
    """
    Gemini LLM provider for Google's Gemini models.
    
    Supports text generation, function calling, streaming, and multimodal input.
    """
    
    def __init__(
        self,
        api_key: Optional[str] = None,
        model: str = "gemini-2.0-flash-exp",
        temperature: float = 0.7,
        max_tokens: int = 8192,
        top_p: float = 0.95,
        top_k: int = 40,
        timeout: int = 60,
        max_retries: int = 3
    ):
        """
        Initialize Gemini provider.
        
        Args:
            api_key: Google AI API key
            model: Model name to use
            temperature: Sampling temperature
            max_tokens: Maximum tokens to generate
            top_p: Top-p sampling parameter
            top_k: Top-k sampling parameter
            timeout: Request timeout in seconds
            max_retries: Maximum number of retries
        """
        self.api_key = api_key or os.getenv("GEMINI_API_KEY")
        if not self.api_key:
            raise ValueError(
                "Gemini API key not found. Set GEMINI_API_KEY environment variable "
                "or pass api_key parameter."
            )
        
        self.model = model
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.top_p = top_p
        self.top_k = top_k
        self.timeout = timeout
        self.max_retries = max_retries
        
        # Configure the API
        genai.configure(api_key=self.api_key)
        
        # Initialize the model
        self._model_instance = None
        self._initialize_model()
    
    def _initialize_model(self) -> None:
        """Initialize the Gemini model instance."""
        try:
            model_info = get_model_info(self.model)
            
            # Configure generation parameters
            generation_config = genai.types.GenerationConfig(
                temperature=self.temperature,
                max_output_tokens=self.max_tokens,
                top_p=self.top_p,
                top_k=self.top_k,
            )
            
            # Configure safety settings (less restrictive for coding tasks)
            safety_settings = {
                HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_NONE,
                HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_NONE,
                HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_NONE,
                HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_NONE,
            }
            
            self._model_instance = genai.GenerativeModel(
                model_name=self.model,
                generation_config=generation_config,
                safety_settings=safety_settings,
            )
            
            logger.info(f"Initialized Gemini model: {model_info.full_name}")
            
        except Exception as e:
            logger.error(f"Failed to initialize Gemini model: {e}")
            raise
    
    def _messages_to_gemini_format(self, messages: List[Message]) -> List[Dict[str, Any]]:
        """Convert messages to Gemini format."""
        gemini_messages = []
        
        for msg in messages:
            # Map roles
            if msg.role == "user":
                role = "user"
            elif msg.role == "assistant":
                role = "model"
            elif msg.role == "system":
                # Gemini doesn't have system role, prepend to first user message
                if not gemini_messages:
                    gemini_messages.append({
                        "role": "user",
                        "parts": [{"text": f"System: {msg.content}"}]
                    })
                    continue
                else:
                    # Add as user message
                    role = "user"
            else:
                continue
            
            # Handle content
            parts = []
            
            # Check if content contains images or other media
            if self._contains_media(msg.content):
                parts.extend(self._extract_media_parts(msg.content))
            else:
                parts.append({"text": msg.content})
            
            gemini_messages.append({
                "role": role,
                "parts": parts
            })
        
        return gemini_messages
    
    def _contains_media(self, content: str) -> bool:
        """Check if content contains media references."""
        # Simple check for image references
        media_indicators = ["![", "data:image/", ".png", ".jpg", ".jpeg", ".gif", ".webp"]
        return any(indicator in content.lower() for indicator in media_indicators)
    
    def _extract_media_parts(self, content: str) -> List[Dict[str, Any]]:
        """Extract media parts from content."""
        parts = []
        
        # For now, just return text content
        # TODO: Implement proper media extraction
        parts.append({"text": content})
        
        return parts
    
    def _prepare_tools(self, tools: Optional[List[Dict[str, Any]]]) -> Optional[List[Any]]:
        """Prepare tools for Gemini function calling."""
        if not tools:
            return None
        
        gemini_tools = []
        
        for tool in tools:
            if tool.get("type") == "function":
                function_def = tool.get("function", {})
                
                # Convert to Gemini function declaration format
                gemini_function = genai.protos.FunctionDeclaration(
                    name=function_def.get("name", ""),
                    description=function_def.get("description", ""),
                    parameters=function_def.get("parameters", {})
                )
                
                gemini_tools.append(genai.protos.Tool(
                    function_declarations=[gemini_function]
                ))
        
        return gemini_tools if gemini_tools else None
    
    def reply(
        self,
        messages: List[Message],
        tools: Optional[List[Dict[str, Any]]] = None,
        **kwargs
    ) -> Message:
        """
        Generate a non-streaming reply.
        
        Args:
            messages: Conversation messages
            tools: Available tools for function calling
            **kwargs: Additional parameters
        
        Returns:
            Generated message
        """
        try:
            # Convert messages to Gemini format
            gemini_messages = self._messages_to_gemini_format(messages)
            
            # Prepare tools
            gemini_tools = self._prepare_tools(tools)
            
            # Generate response
            if gemini_tools:
                response = self._model_instance.generate_content(
                    gemini_messages,
                    tools=gemini_tools,
                    **kwargs
                )
            else:
                response = self._model_instance.generate_content(
                    gemini_messages,
                    **kwargs
                )
            
            # Extract response text
            response_text = ""
            if response.text:
                response_text = response.text
            
            # Handle function calls
            function_calls = []
            if hasattr(response, 'candidates') and response.candidates:
                candidate = response.candidates[0]
                if hasattr(candidate, 'content') and candidate.content.parts:
                    for part in candidate.content.parts:
                        if hasattr(part, 'function_call'):
                            function_calls.append({
                                "name": part.function_call.name,
                                "arguments": dict(part.function_call.args)
                            })
            
            # Create response message
            if function_calls:
                # Include function calls in the response
                response_text += "\n\n" + json.dumps(function_calls, indent=2)
            
            return Message(
                role="assistant",
                content=response_text,
                metadata={
                    "model": self.model,
                    "function_calls": function_calls,
                    "usage": self._extract_usage(response)
                }
            )
            
        except Exception as e:
            logger.error(f"Error generating reply: {e}")
            raise
    
    def stream_reply(
        self,
        messages: List[Message],
        tools: Optional[List[Dict[str, Any]]] = None,
        **kwargs
    ) -> Generator[Message, None, None]:
        """
        Generate a streaming reply.
        
        Args:
            messages: Conversation messages
            tools: Available tools for function calling
            **kwargs: Additional parameters
        
        Yields:
            Message chunks
        """
        try:
            # Convert messages to Gemini format
            gemini_messages = self._messages_to_gemini_format(messages)
            
            # Prepare tools
            gemini_tools = self._prepare_tools(tools)
            
            # Generate streaming response
            if gemini_tools:
                response_stream = self._model_instance.generate_content(
                    gemini_messages,
                    tools=gemini_tools,
                    stream=True,
                    **kwargs
                )
            else:
                response_stream = self._model_instance.generate_content(
                    gemini_messages,
                    stream=True,
                    **kwargs
                )
            
            accumulated_content = ""
            
            for chunk in response_stream:
                if chunk.text:
                    accumulated_content += chunk.text
                    
                    yield Message(
                        role="assistant",
                        content=chunk.text,
                        metadata={
                            "model": self.model,
                            "chunk": True,
                            "accumulated_content": accumulated_content
                        }
                    )
            
            # Final message with complete content
            yield Message(
                role="assistant",
                content=accumulated_content,
                metadata={
                    "model": self.model,
                    "final": True,
                    "usage": self._extract_usage(response_stream)
                }
            )
            
        except Exception as e:
            logger.error(f"Error generating streaming reply: {e}")
            raise
    
    def _extract_usage(self, response) -> Dict[str, Any]:
        """Extract usage information from response."""
        usage = {}
        
        try:
            if hasattr(response, 'usage_metadata'):
                usage_metadata = response.usage_metadata
                usage = {
                    "prompt_tokens": getattr(usage_metadata, 'prompt_token_count', 0),
                    "completion_tokens": getattr(usage_metadata, 'candidates_token_count', 0),
                    "total_tokens": getattr(usage_metadata, 'total_token_count', 0),
                }
        except Exception as e:
            logger.debug(f"Could not extract usage information: {e}")
        
        return usage
    
    def list_models(self) -> List[str]:
        """List available models."""
        try:
            models = genai.list_models()
            return [model.name for model in models if 'generateContent' in model.supported_generation_methods]
        except Exception as e:
            logger.error(f"Error listing models: {e}")
            return ["gemini-2.0-flash-exp", "gemini-1.5-flash", "gemini-1.5-pro"]
    
    def estimate_tokens(self, text: str) -> int:
        """Estimate token count for text."""
        try:
            # Use Gemini's token counting if available
            response = self._model_instance.count_tokens(text)
            return response.total_tokens
        except Exception:
            # Fallback to simple estimation
            return estimate_tokens_simple(text)
    
    def estimate_cost(self, messages: List[Message]) -> float:
        """Estimate cost for messages."""
        try:
            model_info = get_model_info(self.model)
            
            total_input_tokens = 0
            total_output_tokens = 0
            
            for msg in messages:
                tokens = self.estimate_tokens(msg.content)
                
                if msg.role in ["user", "system"]:
                    total_input_tokens += tokens
                elif msg.role == "assistant":
                    total_output_tokens += tokens
            
            return model_info.estimate_cost(total_input_tokens, total_output_tokens)
            
        except Exception as e:
            logger.error(f"Error estimating cost: {e}")
            return 0.0
