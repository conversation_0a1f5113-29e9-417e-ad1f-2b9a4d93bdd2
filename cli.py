"""
Command-line interface for the Advanced AI Agent.

This module provides the main CLI entry point and command handling
for the AI agent application.
"""

import os
import sys
import logging
from pathlib import Path
from typing import List, Optional

import click
from rich.console import Console
from rich.logging import <PERSON>Hand<PERSON>

try:
    from ..aiagent import __version__
except ImportError:
    # When running directly without installation
    import sys
    from pathlib import Path
    sys.path.insert(0, str(Path(__file__).parent.parent))
    try:
        from ..aiagent import __version__
    except ImportError:
        __version__ = "dev"

console = Console()

try:
    from config import get_config, Config
    from chat import chat, create_initial_messages
    from message import create_user_message
    from tools import init_tools, list_available_tools
    from llm import list_available_models
    from utils.logging_setup import setup_logging
except ImportError as e:
    console.print(f"[red]Import error: {e}[/red]")
    console.print("[red]Required dependencies not installed. Please install missing dependencies and try again.[/red]")
    sys.exit(1)




@click.group(invoke_without_command=True)
@click.option(
    "--version",
    is_flag=True,
    help="Show version information"
)
@click.option(
    "-v", "--verbose",
    is_flag=True,
    help="Enable verbose logging"
)
@click.option(
    "--disable-logs",
    is_flag=True,
    help="Disable all console logging"
)
@click.option(
    "--config",
    type=click.Path(exists=True),
    help="Path to configuration file"
)
@click.pass_context
def cli(ctx, version, verbose, config, disable_logs):
    """Advanced AI Agent - A powerful AI assistant with comprehensive tool capabilities."""
    # Setup logging
    setup_logging(verbose=False, disable_console_logs=True)
    
    if version:
        console.print(f"Advanced AI Agent v{__version__}")
        console.print("Powered by Google Gemini 2.0 Flash")
        sys.exit(0)
    
    # Load configuration
    if config:
        config_obj = Config.load(config)
    else:
        config_obj = get_config()
    
    # If no subcommand, run interactive chat
    if ctx.invoked_subcommand is None:
        run_interactive_chat(config_obj)


@cli.command()
@click.argument("prompt", nargs=-1)
@click.option(
    "-m", "--model",
    help="Model to use for generation"
)
@click.option(
    "-t", "--tools",
    help="Comma-separated list of tools to enable"
)
@click.option(
    "-w", "--workspace",
    type=click.Path(exists=True, file_okay=False),
    help="Working directory"
)
@click.option(
    "--stream/--no-stream",
    default=True,
    help="Enable/disable streaming responses"
)
@click.option(
    "--auto-confirm",
    is_flag=True,
    help="Automatically confirm tool executions"
)
@click.option(
    "--conversation-id",
    help="Conversation ID for persistence"
)
def run(prompt, model, tools, workspace, stream, auto_confirm, conversation_id):
    """Run the AI agent with a specific prompt."""
    
    config = get_config()
    
    # Parse tools
    tool_list = None
    if tools:
        tool_list = [t.strip() for t in tools.split(",")]
    
    # Set workspace
    workspace_path = Path(workspace) if workspace else Path.cwd()
    
    # Create initial messages
    initial_messages = create_initial_messages(tools=tool_list)
    
    # Add user prompt if provided
    if prompt:
        user_prompt = " ".join(prompt)
        initial_messages.append(create_user_message(user_prompt))
    
    # Run chat
    chat(
        initial_messages=initial_messages,
        workspace=workspace_path,
        model=model,
        stream=stream,
        interactive=not bool(prompt),  # Non-interactive if prompt provided
        auto_confirm=auto_confirm,
        tool_allowlist=tool_list,
        conversation_id=conversation_id
    )


@cli.command()
def tools():
    """List available tools."""
    
    try:
        # Initialize tools to get availability
        init_tools()
        available_tools = list_available_tools()
        
        console.print(f"\n[bold]Available Tools ({len(available_tools)}):[/bold]\n")
        
        for tool_name in sorted(available_tools):
            # Get tool info (simplified for now)
            console.print(f"  ✓ [green]{tool_name}[/green]")
        
        console.print()
        
    except Exception as e:
        console.print(f"[red]Error listing tools: {e}[/red]")


@cli.command()
def models():
    """List available models."""
    
    try:
        available_models = list_available_models()
        
        console.print(f"\n[bold]Available Models ({len(available_models)}):[/bold]\n")
        
        for model in available_models:
            console.print(f"  • {model}")
        
        console.print()
        
    except Exception as e:
        console.print(f"[red]Error listing models: {e}[/red]")


@cli.command()
@click.option(
    "--show-all",
    is_flag=True,
    help="Show all configuration options"
)
def config(show_all):
    """Show configuration information."""
    
    config_obj = get_config()
    
    console.print("\n[bold]Configuration:[/bold]\n")
    
    # Core settings
    console.print(f"Model: [cyan]{config_obj.llm.model}[/cyan]")
    console.print(f"Temperature: [cyan]{config_obj.llm.temperature}[/cyan]")
    console.print(f"Max Tokens: [cyan]{config_obj.llm.max_tokens}[/cyan]")
    console.print(f"Tools: [cyan]{', '.join(config_obj.tools.enabled)}[/cyan]")
    
    # Paths
    console.print(f"\nConfig Dir: [yellow]{config_obj.config_dir}[/yellow]")
    console.print(f"Data Dir: [yellow]{config_obj.data_dir}[/yellow]")
    console.print(f"Logs Dir: [yellow]{config_obj.logs_dir}[/yellow]")
    console.print(f"Workspace: [yellow]{config_obj.workspace_dir}[/yellow]")
    
    if show_all:
        # Server settings
        console.print(f"\nServer Host: [cyan]{config_obj.server.host}[/cyan]")
        console.print(f"Server Port: [cyan]{config_obj.server.port}[/cyan]")
        console.print(f"Debug Mode: [cyan]{config_obj.server.debug}[/cyan]")
        
        # Features
        console.print(f"\nTelemetry: [cyan]{config_obj.features.telemetry_enabled}[/cyan]")
        console.print(f"Auto Commit: [cyan]{config_obj.features.auto_commit}[/cyan]")
        console.print(f"Cost Tracking: [cyan]{config_obj.features.cost_tracking}[/cyan]")
    
    console.print()


@cli.command()
@click.option(
    "--host",
    default="127.0.0.1",
    help="Host to bind the server to"
)
@click.option(
    "--port",
    default=5000,
    type=int,
    help="Port to bind the server to"
)
@click.option(
    "--debug",
    is_flag=True,
    help="Enable debug mode"
)
def server(host, port, debug):
    """Start the web server."""
    
    try:
        import time
        time.sleep(5)
        from server import create_app
        
        app = create_app()
        
        console.print(f"Starting server on http://{host}:{port}")
        console.print("Press Ctrl+C to stop")
        
        app.run(host=host, port=port, debug=debug)
        
    except ImportError:
        console.print("[red]Server dependencies not installed. Install with: pip install 'advanced-ai-agent[server]'[/red]")
    except Exception as e:
        console.print(f"[red]Error starting server: {e}[/red]")


def run_interactive_chat(config):
    """Run interactive chat session."""
    
    console.print(f"\n[bold green]Advanced AI Agent v{__version__}[/bold green]")
    console.print("Powered by Google Gemini 2.0 Flash\n")
    
    console.print("Type your message and press Enter to chat.")
    console.print("Use /help for commands, /exit to quit.\n")
    
    try:
        # Create initial messages
        initial_messages = create_initial_messages()
        
        # Start chat
        chat(
            initial_messages=initial_messages,
            workspace=config.workspace_dir,
            model=config.llm.model,
            stream=True,
            interactive=True,
            auto_confirm=config.tools.auto_confirm,
            tool_allowlist=config.tools.enabled
        )
        
    except KeyboardInterrupt:
        console.print("\n[yellow]Goodbye![/yellow]")
    except Exception as e:
        console.print(f"\n[red]Error: {e}[/red]")
        if config.server.debug:
            import traceback
            traceback.print_exc()


def main():
    """Main entry point."""
    try:
        cli()
    except KeyboardInterrupt:
        console.print("\n[yellow]Interrupted[/yellow]")
        sys.exit(1)
    except Exception as e:
        console.print(f"[red]Fatal error: {e}[/red]")
        sys.exit(1)


if __name__ == "__main__":
    main()
