"""
Advanced web tools for URL fetching, web search, and web scraping.

This tool provides comprehensive web interaction capabilities including
URL fetching, web search, content extraction, and intelligent web scraping.
"""

import re
import json
import logging
import requests
from typing import Generator, Optional, Dict, Any, List
from urllib.parse import urljoin, urlparse
from pathlib import Path

try:
    from bs4 import BeautifulSoup
    BS4_AVAILABLE = True
except ImportError:
    BS4_AVAILABLE = False

try:
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.chrome.options import Options
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False

from tools.base import ToolSpec, Parameter
from message import Message

logger = logging.getLogger(__name__)


class WebToolsSpec(ToolSpec):
    """Advanced web tools for fetching, searching, and scraping web content."""
    
    def __init__(self):
        super().__init__(
            name="web",
            description="Fetch URLs, search web, scrape content, and perform web automation",
            parameters=[
                Parameter(
                    name="action",
                    type="string",
                    description="Web action to perform",
                    required=True,
                    enum=["fetch", "search", "scrape", "extract", "download"]
                ),
                Parameter(
                    name="url",
                    type="string",
                    description="URL to fetch or base URL for search",
                    required=False
                ),
                Parameter(
                    name="query",
                    type="string",
                    description="Search query or CSS selector for extraction",
                    required=False
                ),
                Parameter(
                    name="format",
                    type="string",
                    description="Output format",
                    required=False,
                    enum=["text", "html", "json", "markdown"],
                    default="text"
                ),
                Parameter(
                    name="max_results",
                    type="integer",
                    description="Maximum number of results",
                    required=False,
                    default=10
                ),
                Parameter(
                    name="timeout",
                    type="integer",
                    description="Request timeout in seconds",
                    required=False,
                    default=30
                )
            ],
            block_types=["web", "url", "fetch", "search", "scrape"]
        )
        self._session = None
        self._driver = None
    
    def is_available(self) -> bool:
        """Check if web tools are available."""
        return True
    
    def _do_init(self) -> None:
        """Initialize web tools."""
        # Initialize requests session
        self._session = requests.Session()
        self._session.headers.update({
            'User-Agent': 'Advanced-AI-Agent/1.0 (Web Tools)'
        })
        
        # Initialize Selenium driver if available
        if SELENIUM_AVAILABLE:
            try:
                options = Options()
                options.add_argument('--headless')
                options.add_argument('--no-sandbox')
                options.add_argument('--disable-dev-shm-usage')
                self._driver = webdriver.Chrome(options=options)
            except Exception as e:
                logger.warning(f"Could not initialize Selenium driver: {e}")
    
    def execute(self, content: str, **kwargs) -> Generator[Message, None, None]:
        """
        Execute web operation.
        
        Args:
            content: Web operation content
            **kwargs: Additional parameters
        
        Yields:
            Response messages with web operation results
        """
        try:
            # Parse content if action not specified
            if "action" not in kwargs:
                action, url, query = self._parse_content(content)
                kwargs.update({
                    "action": action,
                    "url": url,
                    "query": query
                })
            
            # Validate parameters
            params = self.validate_parameters(**kwargs)
            action = params["action"]
            
            # Execute action
            if action == "fetch":
                yield from self._fetch_url(params)
            elif action == "search":
                yield from self._search_web(params)
            elif action == "scrape":
                yield from self._scrape_content(params)
            elif action == "extract":
                yield from self._extract_data(params)
            elif action == "download":
                yield from self._download_file(params)
            else:
                yield self.create_response(f"Unknown web action: {action}")
                
        except Exception as e:
            logger.error(f"Error in web operation: {e}")
            yield self.create_response(self.format_error(e))
    
    def _parse_content(self, content: str) -> tuple[str, str, str]:
        """Parse content to extract action, URL, and query."""
        lines = content.strip().split('\n')
        
        if not lines:
            return "fetch", "", ""
        
        first_line = lines[0].strip()
        
        # Check for URL patterns
        if first_line.startswith(('http://', 'https://')):
            return "fetch", first_line, ""
        
        # Check for search patterns
        if first_line.startswith('search:'):
            return "search", "", first_line[7:].strip()
        
        # Default to search
        return "search", "", first_line
    
    def _fetch_url(self, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Fetch content from a URL."""
        url = params.get("url")
        if not url:
            yield self.create_response("URL is required for fetch action")
            return
        
        timeout = params.get("timeout", 30)
        format_type = params.get("format", "text")
        
        try:
            yield self.create_response(f"Fetching: {url}")
            
            response = self._session.get(url, timeout=timeout)
            response.raise_for_status()
            
            # Process content based on format
            if format_type == "html":
                content = response.text
            elif format_type == "json":
                try:
                    content = json.dumps(response.json(), indent=2)
                except:
                    content = response.text
            elif format_type == "markdown":
                content = self._html_to_markdown(response.text)
            else:  # text
                content = self._extract_text_content(response.text)
            
            # Add metadata
            metadata = {
                "url": url,
                "status_code": response.status_code,
                "content_type": response.headers.get("content-type", ""),
                "content_length": len(content)
            }
            
            yield self.create_response(
                f"Content from {url}:\n\n{content}",
                **metadata
            )
            
        except requests.RequestException as e:
            yield self.create_response(f"Error fetching URL: {e}")
    
    def _search_web(self, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Search the web for information."""
        query = params.get("query")
        if not query:
            yield self.create_response("Query is required for search action")
            return
        
        max_results = params.get("max_results", 10)
        
        try:
            yield self.create_response(f"Searching web for: {query}")
            
            # Use DuckDuckGo search (no API key required)
            search_url = f"https://duckduckgo.com/html/?q={requests.utils.quote(query)}"
            
            response = self._session.get(search_url)
            response.raise_for_status()
            
            if BS4_AVAILABLE:
                soup = BeautifulSoup(response.text, 'html.parser')
                results = []
                
                for result in soup.find_all('div', class_='result')[:max_results]:
                    title_elem = result.find('a', class_='result__a')
                    snippet_elem = result.find('a', class_='result__snippet')
                    
                    if title_elem:
                        title = title_elem.get_text(strip=True)
                        url = title_elem.get('href', '')
                        snippet = snippet_elem.get_text(strip=True) if snippet_elem else ""
                        
                        results.append({
                            "title": title,
                            "url": url,
                            "snippet": snippet
                        })
                
                # Format results
                if results:
                    formatted_results = []
                    for i, result in enumerate(results, 1):
                        formatted_results.append(
                            f"{i}. **{result['title']}**\n"
                            f"   URL: {result['url']}\n"
                            f"   {result['snippet']}\n"
                        )
                    
                    yield self.create_response(
                        f"Search results for '{query}':\n\n" + "\n".join(formatted_results)
                    )
                else:
                    yield self.create_response("No search results found")
            else:
                yield self.create_response("BeautifulSoup not available for parsing search results")
                
        except Exception as e:
            yield self.create_response(f"Error searching web: {e}")
    
    def _scrape_content(self, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Scrape content from a website."""
        url = params.get("url")
        if not url:
            yield self.create_response("URL is required for scrape action")
            return
        
        try:
            yield self.create_response(f"Scraping content from: {url}")
            
            if self._driver and SELENIUM_AVAILABLE:
                # Use Selenium for dynamic content
                self._driver.get(url)
                WebDriverWait(self._driver, 10).until(
                    EC.presence_of_element_located((By.TAG_NAME, "body"))
                )
                
                content = self._driver.page_source
            else:
                # Use requests for static content
                response = self._session.get(url)
                response.raise_for_status()
                content = response.text
            
            # Extract structured data
            if BS4_AVAILABLE:
                soup = BeautifulSoup(content, 'html.parser')
                
                # Extract common elements
                data = {
                    "title": soup.title.string if soup.title else "",
                    "headings": [h.get_text(strip=True) for h in soup.find_all(['h1', 'h2', 'h3'])],
                    "links": [{"text": a.get_text(strip=True), "href": a.get('href')} 
                             for a in soup.find_all('a', href=True)],
                    "images": [{"alt": img.get('alt', ''), "src": img.get('src')} 
                              for img in soup.find_all('img')],
                    "text_content": soup.get_text(strip=True)
                }
                
                yield self.create_response(
                    f"Scraped data from {url}:\n\n" + json.dumps(data, indent=2)
                )
            else:
                yield self.create_response(
                    f"Raw HTML content from {url}:\n\n{content[:2000]}..."
                )
                
        except Exception as e:
            yield self.create_response(f"Error scraping content: {e}")
    
    def _extract_data(self, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Extract specific data using CSS selectors."""
        url = params.get("url")
        query = params.get("query")  # CSS selector
        
        if not url or not query:
            yield self.create_response("URL and CSS selector are required for extract action")
            return
        
        try:
            yield self.create_response(f"Extracting data from {url} using selector: {query}")
            
            response = self._session.get(url)
            response.raise_for_status()
            
            if BS4_AVAILABLE:
                soup = BeautifulSoup(response.text, 'html.parser')
                elements = soup.select(query)
                
                extracted_data = []
                for elem in elements:
                    extracted_data.append({
                        "text": elem.get_text(strip=True),
                        "html": str(elem),
                        "attributes": dict(elem.attrs) if hasattr(elem, 'attrs') else {}
                    })
                
                yield self.create_response(
                    f"Extracted {len(extracted_data)} elements:\n\n" + 
                    json.dumps(extracted_data, indent=2)
                )
            else:
                yield self.create_response("BeautifulSoup not available for data extraction")
                
        except Exception as e:
            yield self.create_response(f"Error extracting data: {e}")
    
    def _download_file(self, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Download a file from URL."""
        url = params.get("url")
        if not url:
            yield self.create_response("URL is required for download action")
            return
        
        try:
            yield self.create_response(f"Downloading file from: {url}")
            
            response = self._session.get(url, stream=True)
            response.raise_for_status()
            
            # Determine filename
            filename = Path(urlparse(url).path).name
            if not filename:
                filename = "downloaded_file"
            
            # Save file
            with open(filename, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            file_size = Path(filename).stat().st_size
            
            yield self.create_response(
                f"Downloaded {filename} ({file_size} bytes) from {url}"
            )
            
        except Exception as e:
            yield self.create_response(f"Error downloading file: {e}")
    
    def _extract_text_content(self, html: str) -> str:
        """Extract clean text content from HTML."""
        if BS4_AVAILABLE:
            soup = BeautifulSoup(html, 'html.parser')
            
            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.decompose()
            
            return soup.get_text(strip=True)
        else:
            # Simple HTML tag removal
            clean = re.compile('<.*?>')
            return re.sub(clean, '', html)
    
    def _html_to_markdown(self, html: str) -> str:
        """Convert HTML to markdown format."""
        if BS4_AVAILABLE:
            soup = BeautifulSoup(html, 'html.parser')
            
            # Simple markdown conversion
            markdown = ""
            
            # Convert headings
            for i in range(1, 7):
                for heading in soup.find_all(f'h{i}'):
                    markdown += f"{'#' * i} {heading.get_text(strip=True)}\n\n"
            
            # Convert paragraphs
            for p in soup.find_all('p'):
                markdown += f"{p.get_text(strip=True)}\n\n"
            
            # Convert links
            for a in soup.find_all('a', href=True):
                text = a.get_text(strip=True)
                href = a.get('href')
                markdown += f"[{text}]({href})\n"
            
            return markdown
        else:
            return self._extract_text_content(html)
    
    def __del__(self):
        """Cleanup resources."""
        if self._driver:
            try:
                self._driver.quit()
            except:
                pass


# Create tool instance
web_tools = WebToolsSpec()
