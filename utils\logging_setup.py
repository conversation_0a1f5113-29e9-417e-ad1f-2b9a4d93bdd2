"""
Logging setup utilities for the AI agent.

This module provides comprehensive logging configuration with support for
multiple handlers, formatters, and log levels.
"""

import logging
import logging.handlers
import sys
from pathlib import Path
from typing import Optional
from rich.logging import RichHandler
from rich.console import Console

console = Console()


def setup_logging(
    level: str = "INFO",
    log_file: Optional[Path] = None,
    verbose: bool = False,
    disable_console_logs: bool = False,
    max_file_size: int = 10 * 1024 * 1024,  # 10MB
    backup_count: int = 5
) -> None:
    """
    Setup comprehensive logging configuration.
    
    Args:
        level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: Path to log file (optional)
        verbose: Enable verbose logging
        disable_console_logs: Disable console logging
        max_file_size: Maximum log file size in bytes
        backup_count: Number of backup log files to keep
    """
    # Convert level string to logging constant
    numeric_level = getattr(logging, level.upper(), logging.INFO)
    
    # Create root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.DEBUG if verbose else numeric_level)
    
    # Clear existing handlers
    root_logger.handlers.clear()
    
    # Console handler with Rich formatting
    if not disable_console_logs:
        console_handler = RichHandler(
            console=console,
            show_time=True,
            show_path=verbose,
            markup=True,
            rich_tracebacks=True
        )
        console_handler.setLevel(numeric_level)
        
        # Console formatter
        console_formatter = logging.Formatter(
            fmt="%(message)s",
            datefmt="[%X]"
        )
        console_handler.setFormatter(console_formatter)
        root_logger.addHandler(console_handler)
    
    # File handler with rotation
    if log_file:
        log_file.parent.mkdir(parents=True, exist_ok=True)
        
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=max_file_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        
        # File formatter (more detailed)
        file_formatter = logging.Formatter(
            fmt='%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(file_formatter)
        root_logger.addHandler(file_handler)
    
    # Suppress noisy third-party loggers
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    logging.getLogger("requests").setLevel(logging.WARNING)
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("selenium").setLevel(logging.WARNING)
    logging.getLogger("werkzeug").setLevel(logging.WARNING)
    
    # Set specific logger levels
    logging.getLogger("aiagent").setLevel(logging.DEBUG if verbose else numeric_level)
    logging.getLogger("tools").setLevel(logging.DEBUG if verbose else numeric_level)
    logging.getLogger("llm").setLevel(logging.DEBUG if verbose else numeric_level)


def get_logger(name: str) -> logging.Logger:
    """
    Get a logger with the specified name.
    
    Args:
        name: Logger name
    
    Returns:
        Configured logger instance
    """
    return logging.getLogger(name)


def log_function_call(func):
    """
    Decorator to log function calls.
    
    Args:
        func: Function to decorate
    
    Returns:
        Decorated function
    """
    def wrapper(*args, **kwargs):
        logger = get_logger(func.__module__)
        logger.debug(f"Calling {func.__name__} with args={args}, kwargs={kwargs}")
        
        try:
            result = func(*args, **kwargs)
            logger.debug(f"{func.__name__} completed successfully")
            return result
        except Exception as e:
            logger.error(f"{func.__name__} failed with error: {e}")
            raise
    
    return wrapper


def log_performance(func):
    """
    Decorator to log function performance.
    
    Args:
        func: Function to decorate
    
    Returns:
        Decorated function with performance logging
    """
    import time
    
    def wrapper(*args, **kwargs):
        logger = get_logger(func.__module__)
        start_time = time.time()
        
        try:
            result = func(*args, **kwargs)
            duration = time.time() - start_time
            logger.debug(f"{func.__name__} completed in {duration:.3f}s")
            return result
        except Exception as e:
            duration = time.time() - start_time
            logger.error(f"{func.__name__} failed after {duration:.3f}s: {e}")
            raise
    
    return wrapper


class ContextLogger:
    """Context manager for logging with additional context."""
    
    def __init__(self, logger: logging.Logger, context: str, level: int = logging.INFO):
        """
        Initialize context logger.
        
        Args:
            logger: Logger instance
            context: Context description
            level: Logging level
        """
        self.logger = logger
        self.context = context
        self.level = level
        self.start_time = None
    
    def __enter__(self):
        """Enter the logging context."""
        self.start_time = time.time()
        self.logger.log(self.level, f"Starting: {self.context}")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Exit the logging context."""
        import time
        duration = time.time() - self.start_time
        
        if exc_type is None:
            self.logger.log(self.level, f"Completed: {self.context} ({duration:.3f}s)")
        else:
            self.logger.error(f"Failed: {self.context} ({duration:.3f}s) - {exc_val}")


def setup_structured_logging(service_name: str = "aiagent") -> None:
    """
    Setup structured logging with JSON format.
    
    Args:
        service_name: Name of the service for logging context
    """
    import json
    from datetime import datetime
    
    class JSONFormatter(logging.Formatter):
        """JSON formatter for structured logging."""
        
        def format(self, record):
            log_entry = {
                "timestamp": datetime.utcnow().isoformat(),
                "service": service_name,
                "level": record.levelname,
                "logger": record.name,
                "message": record.getMessage(),
                "module": record.module,
                "function": record.funcName,
                "line": record.lineno
            }
            
            if record.exc_info:
                log_entry["exception"] = self.formatException(record.exc_info)
            
            return json.dumps(log_entry)
    
    # Apply JSON formatter to all handlers
    root_logger = logging.getLogger()
    for handler in root_logger.handlers:
        if not isinstance(handler, RichHandler):  # Keep Rich formatting for console
            handler.setFormatter(JSONFormatter())


__all__ = [
    "setup_logging",
    "get_logger",
    "log_function_call",
    "log_performance",
    "ContextLogger",
    "setup_structured_logging",
]
