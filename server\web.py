"""
Web interface for the AI agent server.

This module provides the web UI routes and templates
for browser-based interaction with the AI agent.
"""

try:
    from flask import Blueprint, render_template_string, request, redirect, url_for
    FLASK_AVAILABLE = True
except ImportError:
    FLASK_AVAILABLE = False

from __main__ import __version__

if FLASK_AVAILABLE:
    web_bp = Blueprint('web', __name__)
else:
    # Placeholder for when Flask is not available
    class MockBlueprint:
        def route(self, *args, **kwargs):
            def decorator(f):
                return f
            return decorator
    
    web_bp = MockBlueprint()

# Simple HTML template for the web interface
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced AI Agent</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 1rem 2rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .header h1 {
            color: white;
            font-size: 1.5rem;
            font-weight: 600;
        }
        
        .header p {
            color: rgba(255, 255, 255, 0.8);
            margin-top: 0.25rem;
        }
        
        .container {
            flex: 1;
            display: flex;
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
            gap: 2rem;
            width: 100%;
        }
        
        .chat-container {
            flex: 1;
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .chat-messages {
            flex: 1;
            padding: 1.5rem;
            overflow-y: auto;
            max-height: 60vh;
        }
        
        .message {
            margin-bottom: 1rem;
            padding: 1rem;
            border-radius: 8px;
            max-width: 80%;
        }
        
        .message.user {
            background: #e3f2fd;
            margin-left: auto;
            border-bottom-right-radius: 4px;
        }
        
        .message.assistant {
            background: #f5f5f5;
            margin-right: auto;
            border-bottom-left-radius: 4px;
        }
        
        .message.system {
            background: #fff3e0;
            margin: 0 auto;
            text-align: center;
            font-style: italic;
            max-width: 60%;
        }
        
        .message-role {
            font-weight: 600;
            margin-bottom: 0.5rem;
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .message.user .message-role {
            color: #1976d2;
        }
        
        .message.assistant .message-role {
            color: #388e3c;
        }
        
        .message.system .message-role {
            color: #f57c00;
        }
        
        .chat-input {
            padding: 1.5rem;
            border-top: 1px solid #e0e0e0;
            background: #fafafa;
        }
        
        .input-form {
            display: flex;
            gap: 1rem;
        }
        
        .input-field {
            flex: 1;
            padding: 0.75rem 1rem;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 1rem;
            outline: none;
            transition: border-color 0.2s;
        }
        
        .input-field:focus {
            border-color: #667eea;
        }
        
        .send-button {
            padding: 0.75rem 1.5rem;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .send-button:hover {
            background: #5a6fd8;
        }
        
        .sidebar {
            width: 300px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            padding: 1.5rem;
        }
        
        .sidebar h3 {
            margin-bottom: 1rem;
            color: #333;
        }
        
        .tool-list {
            list-style: none;
        }
        
        .tool-item {
            padding: 0.5rem 0;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .tool-status {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #4caf50;
        }
        
        .welcome-message {
            text-align: center;
            color: #666;
            padding: 2rem;
            font-style: italic;
        }
        
        @media (max-width: 768px) {
            .container {
                flex-direction: column;
                padding: 1rem;
            }
            
            .sidebar {
                width: 100%;
                order: -1;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🤖 Advanced AI Agent</h1>
        <p>Powered by Google Gemini 2.0 Flash • Version {{ version }}</p>
    </div>
    
    <div class="container">
        <div class="chat-container">
            <div class="chat-messages" id="messages">
                <div class="welcome-message">
                    <h3>Welcome to Advanced AI Agent!</h3>
                    <p>Start a conversation by typing a message below.</p>
                    <p>I can help you with coding, file management, web browsing, and much more!</p>
                </div>
            </div>
            
            <div class="chat-input">
                <form class="input-form" id="chatForm">
                    <input 
                        type="text" 
                        class="input-field" 
                        id="messageInput" 
                        placeholder="Type your message here..."
                        required
                    >
                    <button type="submit" class="send-button">Send</button>
                </form>
            </div>
        </div>
        
        <div class="sidebar">
            <h3>🛠️ Available Tools</h3>
            <ul class="tool-list">
                <li class="tool-item">
                    <div class="tool-status"></div>
                    <span>Shell Commands</span>
                </li>
                <li class="tool-item">
                    <div class="tool-status"></div>
                    <span>Python Code</span>
                </li>
                <li class="tool-item">
                    <div class="tool-status"></div>
                    <span>File Operations</span>
                </li>
                <li class="tool-item">
                    <div class="tool-status"></div>
                    <span>Web Browsing</span>
                </li>
                <li class="tool-item">
                    <div class="tool-status"></div>
                    <span>Computer Use</span>
                </li>
                <li class="tool-item">
                    <div class="tool-status"></div>
                    <span>GitHub Integration</span>
                </li>
                <li class="tool-item">
                    <div class="tool-status"></div>
                    <span>Document Search</span>
                </li>
                <li class="tool-item">
                    <div class="tool-status"></div>
                    <span>Screenshots</span>
                </li>
            </ul>
            
            <h3 style="margin-top: 2rem;">💡 Quick Tips</h3>
            <ul style="list-style: none; font-size: 0.875rem; color: #666;">
                <li style="margin-bottom: 0.5rem;">• Ask me to write code in any language</li>
                <li style="margin-bottom: 0.5rem;">• Request file operations and management</li>
                <li style="margin-bottom: 0.5rem;">• Get help with system administration</li>
                <li style="margin-bottom: 0.5rem;">• Automate web browsing tasks</li>
            </ul>
        </div>
    </div>
    
    <script>
        const messagesContainer = document.getElementById('messages');
        const chatForm = document.getElementById('chatForm');
        const messageInput = document.getElementById('messageInput');
        
        // Remove welcome message on first interaction
        let isFirstMessage = true;
        
        chatForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const message = messageInput.value.trim();
            if (!message) return;
            
            // Clear welcome message on first interaction
            if (isFirstMessage) {
                messagesContainer.innerHTML = '';
                isFirstMessage = false;
            }
            
            // Add user message
            addMessage('user', message);
            messageInput.value = '';
            
            // Show typing indicator
            const typingId = addMessage('assistant', 'Thinking...');
            
            try {
                // Send message to API
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message,
                        stream: false
                    })
                });
                
                const data = await response.json();
                
                // Remove typing indicator
                document.getElementById(typingId).remove();
                
                if (data.error) {
                    addMessage('system', `Error: ${data.error}`);
                } else {
                    addMessage('assistant', data.response);
                }
                
            } catch (error) {
                // Remove typing indicator
                document.getElementById(typingId).remove();
                addMessage('system', `Error: ${error.message}`);
            }
            
            // Scroll to bottom
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        });
        
        function addMessage(role, content) {
            const messageId = 'msg-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}`;
            messageDiv.id = messageId;
            
            const roleDiv = document.createElement('div');
            roleDiv.className = 'message-role';
            roleDiv.textContent = role === 'user' ? 'You' : role === 'assistant' ? 'AI Assistant' : 'System';
            
            const contentDiv = document.createElement('div');
            contentDiv.textContent = content;
            
            messageDiv.appendChild(roleDiv);
            messageDiv.appendChild(contentDiv);
            
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
            
            return messageId;
        }
        
        // Focus input on load
        messageInput.focus();
    </script>
</body>
</html>
"""

@web_bp.route('/')
def index():
    """Main web interface."""
    return render_template_string(HTML_TEMPLATE, version=__version__)

@web_bp.route('/health')
def web_health():
    """Web health check."""
    return "Advanced AI Agent Web Interface - Healthy"

__all__ = ["web_bp"]
