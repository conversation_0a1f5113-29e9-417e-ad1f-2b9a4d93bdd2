"""
Console utilities for rich output formatting.

This module provides console output utilities using the Rich library
for beautiful terminal output.
"""

import sys
from typing import Optional
from rich.console import Console
from rich.markdown import Markdown
from rich.syntax import Syntax
from rich.panel import Panel
from rich.text import Text

from message import Message

# Global console instance
console = Console()


def print_message(message: Message, end: str = "\n") -> None:
    """
    Print a message with appropriate formatting.
    
    Args:
        message: Message to print
        end: String to append at the end
    """
    if message.quiet:
        return
    
    # Format based on role
    if message.role == "user":
        print_user_message(message, end)
    elif message.role == "assistant":
        print_assistant_message(message, end)
    elif message.role == "system":
        print_system_message(message, end)
    else:
        console.print(f"[dim]{message.role}:[/dim] {message.content}", end=end)


def print_user_message(message: Message, end: str = "\n") -> None:
    """Print a user message."""
    console.print(f"[bold blue]User:[/bold blue] {message.content}", end=end)


def print_assistant_message(message: Message, end: str = "\n") -> None:
    """Print an assistant message with syntax highlighting."""
    content = message.content
    
    # Check if content contains code blocks
    if "```" in content:
        print_formatted_content(content, end)
    else:
        console.print(f"[bold green]Assistant:[/bold green] {content}", end=end)


def print_system_message(message: Message, end: str = "\n") -> None:
    """Print a system message."""
    # Check if it's a tool response
    tool_name = message.metadata.get("tool")
    if tool_name:
        console.print(f"[bold yellow]{tool_name}:[/bold yellow] {message.content}", end=end)
    else:
        console.print(f"[dim]System:[/dim] {message.content}", end=end)


def print_formatted_content(content: str, end: str = "\n") -> None:
    """Print content with code block formatting."""
    lines = content.split('\n')
    current_block = []
    in_code_block = False
    code_language = None
    
    for line in lines:
        if line.startswith('```'):
            if in_code_block:
                # End of code block
                if current_block:
                    code_content = '\n'.join(current_block)
                    if code_language:
                        try:
                            syntax = Syntax(code_content, code_language, theme="monokai")
                            console.print(syntax)
                        except Exception:
                            console.print(f"[dim]{code_content}[/dim]")
                    else:
                        console.print(f"[dim]{code_content}[/dim]")
                current_block = []
                in_code_block = False
                code_language = None
            else:
                # Start of code block
                code_language = line[3:].strip() or None
                in_code_block = True
        elif in_code_block:
            current_block.append(line)
        else:
            # Regular text
            if line.strip():
                console.print(line)
    
    # Handle unclosed code block
    if in_code_block and current_block:
        code_content = '\n'.join(current_block)
        console.print(f"[dim]{code_content}[/dim]")
    
    console.print(end=end)


def print_error(message: str) -> None:
    """Print an error message."""
    console.print(f"[bold red]Error:[/bold red] {message}")


def print_warning(message: str) -> None:
    """Print a warning message."""
    console.print(f"[bold yellow]Warning:[/bold yellow] {message}")


def print_info(message: str) -> None:
    """Print an info message."""
    console.print(f"[bold blue]Info:[/bold blue] {message}")


def print_success(message: str) -> None:
    """Print a success message."""
    console.print(f"[bold green]Success:[/bold green] {message}")


def print_panel(content: str, title: Optional[str] = None, style: str = "blue") -> None:
    """Print content in a panel."""
    panel = Panel(content, title=title, border_style=style)
    console.print(panel)


def print_markdown(content: str) -> None:
    """Print markdown content."""
    md = Markdown(content)
    console.print(md)


def print_separator(char: str = "─", length: Optional[int] = None) -> None:
    """Print a separator line."""
    if length is None:
        length = console.size.width
    console.print(char * length)


def clear_screen() -> None:
    """Clear the console screen."""
    console.clear()


def get_terminal_size() -> tuple[int, int]:
    """Get terminal size as (width, height)."""
    size = console.size
    return size.width, size.height


def print_table(data: list[list[str]], headers: Optional[list[str]] = None) -> None:
    """Print data in a table format."""
    from rich.table import Table
    
    table = Table()
    
    # Add columns
    if headers:
        for header in headers:
            table.add_column(header, style="bold")
    else:
        for i in range(len(data[0]) if data else 0):
            table.add_column(f"Column {i+1}")
    
    # Add rows
    for row in data:
        table.add_row(*[str(cell) for cell in row])
    
    console.print(table)


def print_progress(message: str) -> None:
    """Print a progress message."""
    console.print(f"[dim]⏳ {message}[/dim]")


def print_spinner(message: str) -> None:
    """Print a message with spinner (placeholder for now)."""
    console.print(f"[dim]🔄 {message}[/dim]")


__all__ = [
    "console",
    "print_message",
    "print_user_message",
    "print_assistant_message", 
    "print_system_message",
    "print_formatted_content",
    "print_error",
    "print_warning",
    "print_info",
    "print_success",
    "print_panel",
    "print_markdown",
    "print_separator",
    "clear_screen",
    "get_terminal_size",
    "print_table",
    "print_progress",
    "print_spinner",
]
