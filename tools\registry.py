"""
Tool registry for managing and discovering tools.

This module provides the ToolRegistry class that handles
tool discovery, registration, and management.
"""

import importlib
import inspect
import logging
import pkgutil
from typing import Dict, List, Optional, Type
from pathlib import Path

from tools.base import ToolSpec

logger = logging.getLogger(__name__)


class ToolRegistry:
    """
    Registry for managing tools.
    
    Handles tool discovery, registration, and retrieval.
    """
    
    def __init__(self):
        """Initialize the tool registry."""
        self._tools: Dict[str, ToolSpec] = {}
        self._initialized_tools: List[ToolSpec] = []
        self._discovered = False
    
    def register_tool(self, tool: ToolSpec) -> None:
        """
        Register a tool.
        
        Args:
            tool: Tool to register
        """
        self._tools[tool.name] = tool
        logger.debug(f"Registered tool: {tool.name}")
    
    def get_tool(self, name: str) -> Optional[ToolSpec]:
        """
        Get a tool by name.
        
        Args:
            name: Tool name
        
        Returns:
            Tool instance or None if not found
        """
        return self._tools.get(name)
    
    def list_available_tools(self) -> List[str]:
        """List all available tool names."""
        return [name for name, tool in self._tools.items() if tool.is_available()]
    
    def list_all_tools(self) -> List[str]:
        """List all registered tool names."""
        return list(self._tools.keys())
    
    def get_initialized_tools(self) -> List[ToolSpec]:
        """Get all initialized tools."""
        return self._initialized_tools.copy()
    
    def discover_tools(self, package_name: str = "aiagent.tools") -> None:
        """
        Discover and register tools from a package.
        
        Args:
            package_name: Package to discover tools from
        """
        if self._discovered:
            return
        
        try:
            # Import the tools package
            package = importlib.import_module(package_name)
            
            # Get the package path
            if hasattr(package, '__path__'):
                package_path = package.__path__
            else:
                logger.warning(f"Package {package_name} has no __path__")
                return
            
            # Iterate through modules in the package
            for _, module_name, _ in pkgutil.iter_modules(package_path):
                if module_name.startswith('_'):
                    continue
                
                full_module_name = f"{package_name}.{module_name}"
                
                try:
                    module = importlib.import_module(full_module_name)
                    self._discover_tools_in_module(module)
                except ImportError as e:
                    logger.warning(f"Could not import {full_module_name}: {e}")
                except Exception as e:
                    logger.error(f"Error discovering tools in {full_module_name}: {e}")
            
            self._discovered = True
            logger.info(f"Discovered {len(self._tools)} tools")
            
        except Exception as e:
            logger.error(f"Error discovering tools: {e}")
    
    def _discover_tools_in_module(self, module) -> None:
        """
        Discover tools in a specific module.
        
        Args:
            module: Module to search for tools
        """
        # Look for ToolSpec instances
        for name, obj in inspect.getmembers(module):
            if isinstance(obj, ToolSpec):
                self.register_tool(obj)
        
        # Look for ToolSpec classes and instantiate them
        for name, obj in inspect.getmembers(module, inspect.isclass):
            if (issubclass(obj, ToolSpec) and 
                obj is not ToolSpec and 
                not inspect.isabstract(obj)):
                try:
                    # Try to instantiate the tool
                    tool_instance = obj()
                    self.register_tool(tool_instance)
                except Exception as e:
                    logger.warning(f"Could not instantiate tool {name}: {e}")
    
    def initialize_tool(self, name: str) -> Optional[ToolSpec]:
        """
        Initialize a tool by name.
        
        Args:
            name: Tool name to initialize
        
        Returns:
            Initialized tool or None if not found/failed
        """
        tool = self.get_tool(name)
        if not tool:
            logger.warning(f"Tool '{name}' not found")
            return None
        
        if not tool.is_available():
            logger.warning(f"Tool '{name}' is not available")
            return None
        
        try:
            initialized_tool = tool.init()
            if initialized_tool not in self._initialized_tools:
                self._initialized_tools.append(initialized_tool)
            return initialized_tool
        except Exception as e:
            logger.error(f"Failed to initialize tool '{name}': {e}")
            return None
    
    def initialize_tools(self, tool_names: List[str]) -> List[ToolSpec]:
        """
        Initialize multiple tools.
        
        Args:
            tool_names: List of tool names to initialize
        
        Returns:
            List of successfully initialized tools
        """
        initialized = []
        
        for name in tool_names:
            tool = self.initialize_tool(name)
            if tool:
                initialized.append(tool)
        
        return initialized
    
    def clear(self) -> None:
        """Clear all registered tools."""
        self._tools.clear()
        self._initialized_tools.clear()
        self._discovered = False
    
    def get_tools_by_block_type(self, block_type: str) -> List[ToolSpec]:
        """
        Get tools that handle a specific block type.
        
        Args:
            block_type: Block type to search for
        
        Returns:
            List of tools that handle the block type
        """
        return [
            tool for tool in self._tools.values()
            if block_type in tool.block_types
        ]
    
    def get_runnable_tools(self) -> List[ToolSpec]:
        """Get all runnable tools."""
        return [tool for tool in self._tools.values() if tool.is_runnable]
    
    def get_available_tools(self) -> List[ToolSpec]:
        """Get all available tools."""
        return [tool for tool in self._tools.values() if tool.is_available()]
    
    def export_tool_definitions(self) -> List[Dict[str, any]]:
        """
        Export tool definitions for LLM function calling.
        
        Returns:
            List of tool definitions in OpenAI function calling format
        """
        definitions = []
        
        for tool in self.get_available_tools():
            if not tool.is_runnable:
                continue
            
            definition = {
                "type": "function",
                "function": {
                    "name": tool.name,
                    "description": tool.description,
                    "parameters": {
                        "type": "object",
                        "properties": {},
                        "required": []
                    }
                }
            }
            
            # Add parameters
            for param in tool.parameters:
                definition["function"]["parameters"]["properties"][param.name] = {
                    "type": param.type,
                    "description": param.description
                }
                
                if param.enum:
                    definition["function"]["parameters"]["properties"][param.name]["enum"] = param.enum
                
                if param.required:
                    definition["function"]["parameters"]["required"].append(param.name)
            
            definitions.append(definition)
        
        return definitions
    
    def get_tool_info(self) -> Dict[str, Dict[str, any]]:
        """
        Get information about all tools.
        
        Returns:
            Dictionary with tool information
        """
        info = {}
        
        for name, tool in self._tools.items():
            info[name] = {
                "name": tool.name,
                "description": tool.description,
                "is_available": tool.is_available(),
                "is_runnable": tool.is_runnable,
                "disabled_by_default": tool.disabled_by_default,
                "block_types": tool.block_types,
                "parameters": [
                    {
                        "name": param.name,
                        "type": param.type,
                        "description": param.description,
                        "required": param.required,
                        "default": param.default,
                        "enum": param.enum
                    }
                    for param in tool.parameters
                ]
            }
        
        return info
    
    def __len__(self) -> int:
        """Get the number of registered tools."""
        return len(self._tools)
    
    def __contains__(self, name: str) -> bool:
        """Check if a tool is registered."""
        return name in self._tools
    
    def __iter__(self):
        """Iterate over tool names."""
        return iter(self._tools.keys())
    
    def __repr__(self) -> str:
        """String representation of the registry."""
        return f"ToolRegistry({len(self._tools)} tools, {len(self._initialized_tools)} initialized)"
